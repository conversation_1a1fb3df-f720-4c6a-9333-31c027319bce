'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import ContentModal from '../content-modal'

interface ChapterFormData {
  title: string
  description: string
  order: number
  isPublished: boolean
}

interface ChapterModalProps {
  isOpen: boolean
  onClose: () => void
  courseId: string
  sectionId: string
  onSuccess: () => void
  editingChapter?: {
    id: string
    title: string
    description: string
    order: number
    isPublished: boolean
  }
}

export default function ChapterModal({
  isOpen,
  onClose,
  courseId,
  sectionId,
  onSuccess,
  editingChapter
}: ChapterModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<ChapterFormData>({
    title: editingChapter?.title || '',
    description: editingChapter?.description || '',
    order: editingChapter?.order || 1,
    isPublished: editingChapter?.isPublished || false
  })

  // Update form data when editing<PERSON>hapter changes
  useEffect(() => {
    if (editingChapter) {
      setFormData({
        title: editingChapter.title || '',
        description: editingChapter.description || '',
        order: editingChapter.order || 1,
        isPublished: editingChapter.isPublished || false
      })
    } else {
      setFormData({
        title: '',
        description: '',
        order: 1,
        isPublished: false
      })
    }
  }, [editingChapter])

  const handleInputChange = (field: keyof ChapterFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Chapter title is required')
      return
    }

    try {
      setLoading(true)
      
      const url = editingChapter 
        ? `/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${editingChapter.id}`
        : `/api/admin/courses/${courseId}/sections/${sectionId}/chapters`
      
      const method = editingChapter ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || `Failed to ${editingChapter ? 'update' : 'create'} chapter`)
      }

      const result = await response.json()
      const data = result.data || result
      
      toast.success(data.message || `Chapter ${editingChapter ? 'updated' : 'created'} successfully!`)
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        order: 1,
        isPublished: false
      })
      
      onSuccess()
      onClose()

    } catch (error: any) {
      console.error(`Error ${editingChapter ? 'updating' : 'creating'} chapter:`, error)
      toast.error(error.message || `Failed to ${editingChapter ? 'update' : 'create'} chapter`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!editingChapter) {
      setFormData({
        title: '',
        description: '',
        order: 1,
        isPublished: false
      })
    }
    onClose()
  }

  return (
    <ContentModal
      isOpen={isOpen}
      onClose={handleClose}
      title={editingChapter ? 'Edit Chapter' : 'Add New Chapter'}
      description={editingChapter ? 'Update chapter information and settings' : 'Create a new chapter to organize lessons within this section'}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Chapter Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Chapter Title *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            placeholder="Enter chapter title"
            required
          />
        </div>

        {/* Chapter Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={4}
            className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
            placeholder="Describe what this chapter covers"
          />
        </div>

        {/* Order */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Order
          </label>
          <input
            type="number"
            min="1"
            value={formData.order}
            onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 1)}
            className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            placeholder="1"
          />
          <p className="text-sm text-gray-500 mt-1">
            The order in which this chapter appears in the section
          </p>
        </div>

        {/* Published Status */}
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="isPublished"
            checked={formData.isPublished}
            onChange={(e) => handleInputChange('isPublished', e.target.checked)}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
          />
          <label htmlFor="isPublished" className="text-sm font-medium text-gray-700">
            {editingChapter ? 'Chapter is published' : 'Publish chapter immediately'}
          </label>
        </div>

        {/* Submit Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <motion.button
            type="button"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleClose}
            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
          >
            Cancel
          </motion.button>
          <motion.button
            type="submit"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            disabled={loading}
            className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
          >
            {loading ? (editingChapter ? 'Updating...' : 'Creating...') : (editingChapter ? 'Update Chapter' : 'Create Chapter')}
          </motion.button>
        </div>
      </form>
    </ContentModal>
  )
}
