'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import {
  GraduationCap,
  RefreshCw,
  AlertCircle
} from 'lucide-react'
import { CourseGrid } from '@/components/courses/course-grid'
import { CourseFilters, type CourseFilters as Filters } from '@/components/courses/course-filters'
import { type Course } from '@/components/courses/course-card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'



interface Category {
  name: string
  courseCount: number
  icon: string
  color: string
}

export default function CoursesPage() {
  const router = useRouter()
  const [courses, setCourses] = useState<Course[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSyncing, setIsSyncing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    totalPages: 1,
    hasNext: false,
    hasPrev: false
  })
  
  const [filters, setFilters] = useState<Filters>({
    search: '',
    category: '',
    minPrice: 0,
    maxPrice: 10000,
    sortBy: 'popular'
  })

  const fetchCourses = useCallback(async (page = 1, sync = false) => {
    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        sync: sync.toString()
      })

      if (filters.search) params.append('search', filters.search)
      if (filters.category) params.append('category', filters.category)
      if (filters.minPrice > 0) params.append('minPrice', filters.minPrice.toString())
      if (filters.maxPrice < 10000) params.append('maxPrice', filters.maxPrice.toString())

      const response = await fetch(`/api/courses?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch courses')
      }

      const result = await response.json()
      // Handle APIResponse format: { success: true, data: { courses: [...], pagination: {...} } }
      const data = result.data || result

      setCourses(data.courses || [])
      setPagination({
        page: data.pagination?.page || page,
        totalPages: data.pagination?.totalPages || 1,
        hasNext: data.pagination?.hasNext || false,
        hasPrev: data.pagination?.hasPrev || false
      })

    } catch (error) {
      console.error('Error fetching courses:', error)
      setError('Failed to load courses. Please try again.')
      toast.error('Failed to load courses')
    } finally {
      setIsLoading(false)
      setIsSyncing(false)
    }
  }, [filters])

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch('/api/courses/categories')
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }

      const data = await response.json()
      setCategories(data.categories || [])
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }, [])

  const handleFiltersChange = (newFilters: Partial<Filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handlePageChange = (page: number) => {
    fetchCourses(page)
  }

  const handleSync = async () => {
    setIsSyncing(true)
    await fetchCourses(1, true)
    toast.success('Courses synced successfully')
  }

  const handleEnroll = async (courseId: string) => {
    // Find the course to check if user is already enrolled
    const course = courses.find(c => c.id === courseId)

    if (course?.isEnrolled) {
      // If already enrolled, redirect to course learning page
      router.push(`/student/courses/${course.slug}`)
      toast.success('Opening your enrolled course...')
      return
    }

    // If not enrolled, proceed with enrollment
    try {
      const response = await fetch('/api/courses/enroll', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ courseId })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to enroll')
      }

      const result = await response.json()
      // Handle APIResponse format: { success: true, data: { ... } }
      const data = result.data || result

      if (data.redirectUrl) {
        if (data.requiresPayment) {
          toast.success('Redirecting to course page for payment...')
        } else if (data.requiresManualEnrollment) {
          toast.success('Please complete enrollment on the course page')
        } else {
          toast.success('Enrollment successful! Redirecting to course...')
        }
        window.open(data.redirectUrl, '_blank')
      }

      if (!data.requiresPayment) {
        toast.success('Enrolled successfully!')
        fetchCourses(pagination.page)
      }

    } catch (error) {
      console.error('Error enrolling in course:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to enroll in course')
    }
  }

  const handleViewDetails = (courseId: string) => {
    // Find the course to get its slug and enrollment status
    const course = courses.find(c => c.id === courseId)

    if (course) {
      if (course.isEnrolled) {
        // If enrolled, go to learning page
        router.push(`/student/courses/${course.slug || courseId}`)
      } else {
        // If not enrolled, go to course preview page
        router.push(`/courses/${course.slug || courseId}`)
      }
    } else {
      // Fallback: go to preview page
      router.push(`/courses/${courseId}`)
    }
  }

  const handleToggleFavorite = (_courseId: string) => {
    // TODO: Implement favorites functionality
    toast.info('Favorites feature coming soon!')
  }

  const handleShare = (course: Course) => {
    if (navigator.share) {
      navigator.share({
        title: course.title,
        text: course.description,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('Course link copied to clipboard!')
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchCourses(1)
    }, 300) // Debounce filter changes

    return () => clearTimeout(timeoutId)
  }, [fetchCourses])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <GraduationCap className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Courses
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Discover and enroll in professional courses
                  </p>
                </div>
              </div>
            </div>
            
            <Button
              variant="outline"
              onClick={handleSync}
              disabled={isSyncing}
              className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
              Sync Courses
            </Button>
          </div>
        </motion.div>

        {/* Error Alert */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6"
          >
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-8"
        >
          <CourseFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            categories={categories}
            isLoading={isSyncing}
            onSync={handleSync}
          />
        </motion.div>

        {/* Course Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <CourseGrid
            courses={courses}
            isLoading={isLoading}
            onEnroll={handleEnroll}
            onViewDetails={handleViewDetails}
            onToggleFavorite={handleToggleFavorite}
            onShare={handleShare}
            pagination={pagination}
            onPageChange={handlePageChange}
          />
        </motion.div>
      </div>
    </div>
  )
}
