import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createSectionSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional()
})

// GET /api/admin/courses/[id]/sections - Get course sections
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true, title: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      const sections = await prisma.courseSection.findMany({
        where: { courseId },
        orderBy: { createdAt: 'asc' },
        include: {
          chapters: {
            orderBy: { createdAt: 'asc' },
            include: {
              lessons: {
                orderBy: { createdAt: 'asc' },
                select: {
                  id: true,
                  title: true,
                  type: true,
                  duration: true,
                  isPublished: true
                }
              }
            }
          }
        }
      })

      // Calculate section statistics
      const sectionsWithStats = sections.map(section => {
        const totalLessons = section.chapters.reduce((acc, chapter) => 
          acc + chapter.lessons.length, 0)
        
        const totalDuration = section.chapters.reduce((acc, chapter) => 
          acc + chapter.lessons.reduce((lessonAcc, lesson) => 
            lessonAcc + (lesson.duration || 0), 0), 0)

        return {
          ...section,
          totalChapters: section.chapters.length,
          totalLessons,
          totalDuration: Math.round(totalDuration / 60) // Convert to minutes
        }
      })

      return APIResponse.success({
        course,
        sections: sectionsWithStats
      })
    } catch (error) {
      console.error('Error fetching course sections:', error)
      return APIResponse.error('Failed to fetch course sections', 500)
    }
  }
)

// POST /api/admin/courses/[id]/sections - Create new section
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createSectionSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      const section = await prisma.courseSection.create({
        data: {
          courseId,
          title: validatedBody.title,
          description: validatedBody.description
        },
        include: {
          chapters: {
            orderBy: { createdAt: 'asc' },
            include: {
              lessons: {
                orderBy: { createdAt: 'asc' },
                select: {
                  id: true,
                  title: true,
                  type: true,
                  duration: true,
                  isPublished: true
                }
              }
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Section created successfully',
        section
      })
    } catch (error) {
      console.error('Error creating section:', error)
      return APIResponse.error('Failed to create section', 500)
    }
  }
)


