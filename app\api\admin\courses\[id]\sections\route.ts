import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createSectionSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  order: z.number().int().min(0).optional()
})

const updateSectionOrderSchema = z.object({
  sections: z.array(z.object({
    id: z.string(),
    order: z.number().int().min(0)
  }))
})

// GET /api/admin/courses/[id]/sections - Get course sections
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true, title: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      const sections = await prisma.courseSection.findMany({
        where: { courseId },
        orderBy: { order: 'asc' },
        include: {
          chapters: {
            orderBy: { order: 'asc' },
            include: {
              lessons: {
                orderBy: { order: 'asc' },
                select: {
                  id: true,
                  title: true,
                  type: true,
                  duration: true,
                  isPublished: true
                }
              }
            }
          }
        }
      })

      // Calculate section statistics
      const sectionsWithStats = sections.map(section => {
        const totalLessons = section.chapters.reduce((acc, chapter) => 
          acc + chapter.lessons.length, 0)
        
        const totalDuration = section.chapters.reduce((acc, chapter) => 
          acc + chapter.lessons.reduce((lessonAcc, lesson) => 
            lessonAcc + (lesson.duration || 0), 0), 0)

        return {
          ...section,
          totalChapters: section.chapters.length,
          totalLessons,
          totalDuration: Math.round(totalDuration / 60) // Convert to minutes
        }
      })

      return APIResponse.success({
        course,
        sections: sectionsWithStats
      })
    } catch (error) {
      console.error('Error fetching course sections:', error)
      return APIResponse.error('Failed to fetch course sections', 500)
    }
  }
)

// POST /api/admin/courses/[id]/sections - Create new section
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createSectionSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Get the next order number if not provided
      let order = validatedBody.order
      if (order === undefined) {
        const lastSection = await prisma.courseSection.findFirst({
          where: { courseId },
          orderBy: { order: 'desc' },
          select: { order: true }
        })
        order = (lastSection?.order || 0) + 1
      }

      const section = await prisma.courseSection.create({
        data: {
          courseId,
          title: validatedBody.title,
          description: validatedBody.description,
          order
        },
        include: {
          chapters: {
            orderBy: { order: 'asc' },
            include: {
              lessons: {
                orderBy: { order: 'asc' },
                select: {
                  id: true,
                  title: true,
                  type: true,
                  duration: true,
                  isPublished: true
                }
              }
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Section created successfully',
        section
      })
    } catch (error) {
      console.error('Error creating section:', error)
      return APIResponse.error('Failed to create section', 500)
    }
  }
)

// PUT /api/admin/courses/[id]/sections/reorder - Update section order
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateSectionOrderSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Update section orders in a transaction
      await prisma.$transaction(
        validatedBody.sections.map(section =>
          prisma.courseSection.update({
            where: { 
              id: section.id,
              courseId // Ensure section belongs to this course
            },
            data: { order: section.order }
          })
        )
      )

      // Fetch updated sections
      const updatedSections = await prisma.courseSection.findMany({
        where: { courseId },
        orderBy: { order: 'asc' },
        include: {
          chapters: {
            orderBy: { order: 'asc' },
            include: {
              lessons: {
                orderBy: { order: 'asc' },
                select: {
                  id: true,
                  title: true,
                  type: true,
                  duration: true,
                  isPublished: true
                }
              }
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Section order updated successfully',
        sections: updatedSections
      })
    } catch (error) {
      console.error('Error updating section order:', error)
      return APIResponse.error('Failed to update section order', 500)
    }
  }
)
