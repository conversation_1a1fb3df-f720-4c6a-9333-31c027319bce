import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { getBunnyStorageWithTest } from '@/lib/bunny-storage'

// POST /api/test/upload - Test upload endpoint for debugging
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File

      if (!file) {
        return APIResponse.error('No file provided', 400)
      }

      console.log('Test upload - File details:', {
        name: file.name,
        type: file.type,
        size: file.size
      })

      // Get Bunny storage instance with connection testing
      const bunnyStorage = await getBunnyStorageWithTest()
      console.log('Storage instance obtained:', bunnyStorage.constructor.name)

      // Test connection if available
      if (bunnyStorage.testConnection) {
        console.log('Testing connection...')
        const canConnect = await bunnyStorage.testConnection()
        console.log('Connection test result:', canConnect)
      }

      // Upload file
      console.log('Starting upload...')
      const uploadResult = await bunnyStorage.uploadFile(file, {
        folder: 'test-uploads',
        maxSize: 10 * 1024 * 1024 // 10MB
      })

      console.log('Upload result:', uploadResult)

      if (!uploadResult.success) {
        return APIResponse.error(
          `Upload failed: ${uploadResult.error}`,
          500
        )
      }

      return APIResponse.success({
        message: 'Test upload successful',
        file: {
          url: uploadResult.url,
          filename: uploadResult.filename,
          size: uploadResult.size,
          originalName: file.name,
          type: file.type
        },
        storageType: bunnyStorage.constructor.name,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      console.error('Test upload error:', error)
      return APIResponse.error(
        `Test upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      )
    }
  }
)

// GET /api/test/upload - Get upload test info
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      // Check environment variables
      const envVars = {
        BUNNY_STORAGE_ZONE_NAME: !!process.env.BUNNY_STORAGE_ZONE_NAME,
        BUNNY_STORAGE_ACCESS_KEY: !!process.env.BUNNY_STORAGE_ACCESS_KEY,
        BUNNY_PULL_ZONE_URL: !!process.env.BUNNY_PULL_ZONE_URL,
        BUNNY_STORAGE_REGION: process.env.BUNNY_STORAGE_REGION || 'storage'
      }

      // Test storage instance
      const bunnyStorage = await getBunnyStorageWithTest()
      const storageType = bunnyStorage.constructor.name

      // Test connection if available
      let connectionStatus = 'unknown'
      if (bunnyStorage.testConnection) {
        try {
          const canConnect = await bunnyStorage.testConnection()
          connectionStatus = canConnect ? 'connected' : 'failed'
        } catch (error) {
          connectionStatus = 'error'
        }
      } else {
        connectionStatus = 'fallback'
      }

      return APIResponse.success({
        message: 'Upload test endpoint ready',
        environment: envVars,
        storageType,
        connectionStatus,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      console.error('Test upload info error:', error)
      return APIResponse.error(
        `Failed to get test info: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      )
    }
  }
)
