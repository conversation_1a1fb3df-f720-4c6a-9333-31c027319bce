"use client"

import React from 'react'

interface SafeRenderProps {
  data: any
  fallback?: React.ReactNode
  property?: string
  className?: string
}

/**
 * SafeRender component prevents "Objects are not valid as a React child" errors
 * by safely rendering objects and providing fallbacks
 */
export function SafeRender({ 
  data, 
  fallback = "N/A", 
  property,
  className 
}: SafeRenderProps) {
  // If data is null or undefined
  if (data == null) {
    return <span className={className}>{fallback}</span>
  }

  // If a specific property is requested
  if (property) {
    const value = data[property]
    if (value == null) {
      return <span className={className}>{fallback}</span>
    }
    // If the property value is an object, try to render its name or toString
    if (typeof value === 'object') {
      return <span className={className}>{value.name || value.title || String(value)}</span>
    }
    return <span className={className}>{String(value)}</span>
  }

  // If data is a primitive type, render it directly
  if (typeof data === 'string' || typeof data === 'number' || typeof data === 'boolean') {
    return <span className={className}>{String(data)}</span>
  }

  // If data is an object, try to render a meaningful representation
  if (typeof data === 'object') {
    // Try common properties for display
    const displayValue = data.name || data.title || data.label || data.text || data.value
    if (displayValue != null) {
      return <span className={className}>{String(displayValue)}</span>
    }
    
    // If it's an array, show the length
    if (Array.isArray(data)) {
      return <span className={className}>{data.length} items</span>
    }
    
    // Last resort: show object type
    return <span className={className}>[Object]</span>
  }

  // Fallback for any other type
  return <span className={className}>{String(data)}</span>
}

/**
 * SafeUser component for rendering user objects safely
 */
export function SafeUser({ 
  user, 
  property = 'name',
  fallback = 'Unknown User',
  className 
}: { 
  user: any
  property?: string
  fallback?: string
  className?: string 
}) {
  return (
    <SafeRender 
      data={user} 
      property={property} 
      fallback={fallback}
      className={className}
    />
  )
}

/**
 * SafeCourse component for rendering course objects safely
 */
export function SafeCourse({ 
  course, 
  property = 'title',
  fallback = 'Unknown Course',
  className 
}: { 
  course: any
  property?: string
  fallback?: string
  className?: string 
}) {
  return (
    <SafeRender 
      data={course} 
      property={property} 
      fallback={fallback}
      className={className}
    />
  )
}

/**
 * SafeInstructor component for rendering instructor objects safely
 */
export function SafeInstructor({ 
  instructor, 
  property = 'name',
  fallback = 'Unknown Instructor',
  className 
}: { 
  instructor: any
  property?: string
  fallback?: string
  className?: string 
}) {
  return (
    <SafeRender 
      data={instructor} 
      property={property} 
      fallback={fallback}
      className={className}
    />
  )
}

/**
 * DebugRender component for development - shows object structure safely
 * Only renders in development mode
 */
export function DebugRender({ 
  data, 
  label,
  className = "text-xs text-gray-500 font-mono" 
}: { 
  data: any
  label?: string
  className?: string 
}) {
  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const safeData = React.useMemo(() => {
    try {
      return JSON.stringify(data, null, 2)
    } catch (error) {
      return `[Circular Reference or Non-serializable: ${typeof data}]`
    }
  }, [data])

  return (
    <details className={className}>
      <summary className="cursor-pointer">
        {label ? `Debug: ${label}` : 'Debug Data'}
      </summary>
      <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
        {safeData}
      </pre>
    </details>
  )
}
