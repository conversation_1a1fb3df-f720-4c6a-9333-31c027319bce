import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createLessonSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  type: z.enum(['VIDEO', 'TEXT', 'QUIZ', 'ASSIGNMENT', 'DOCUMENT']).default('VIDEO'),
  order: z.number().min(1).default(1),
  duration: z.number().min(0).default(0),
  isPublished: z.boolean().default(false),
  isFree: z.boolean().default(false),
  content: z.string().optional(),
  videoUrl: z.string().optional(),
  attachments: z.array(z.any()).optional(),
  hasQuiz: z.boolean().default(false),
  quizId: z.string().optional()
})

// POST /api/admin/courses/[id]/sections/[sectionId]/chapters/[chapterId]/lessons - Create lesson
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createLessonSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string

      if (!courseId || !sectionId || !chapterId) {
        return APIResponse.error('Course ID, Section ID, and Chapter ID are required', 400)
      }

      // Verify chapter exists
      const chapter = await prisma.courseChapter.findFirst({
        where: {
          id: chapterId,
          sectionId: sectionId,
          section: {
            courseId: courseId
          }
        }
      })

      if (!chapter) {
        return APIResponse.error('Chapter not found', 404)
      }

      // Create lesson data (exclude fields that don't belong in the lesson table)
      const { hasQuiz, quizId, videoUrl, ...lessonFields } = validatedBody

      const lessonData: any = {
        ...lessonFields,
        chapterId: chapterId,
        attachments: validatedBody.attachments || []
      }

      // If it's a video lesson, create video record
      if (validatedBody.type === 'VIDEO' && videoUrl) {
        lessonData.video = {
          create: {
            url: videoUrl,
            duration: validatedBody.duration,
            title: validatedBody.title
          }
        }
      }

      // Create lesson
      const lesson = await prisma.courseLesson.create({
        data: lessonData,
        include: {
          video: true
        }
      })

      return APIResponse.success({
        message: 'Lesson created successfully',
        lesson
      })
    } catch (error) {
      console.error('Error creating lesson:', error)
      return APIResponse.error('Failed to create lesson', 500)
    }
  }
)

// GET /api/admin/courses/[id]/sections/[sectionId]/chapters/[chapterId]/lessons - Get lessons
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string

      if (!courseId || !sectionId || !chapterId) {
        return APIResponse.error('Course ID, Section ID, and Chapter ID are required', 400)
      }

      const lessons = await prisma.courseLesson.findMany({
        where: {
          chapterId: chapterId,
          chapter: {
            sectionId: sectionId,
            section: {
              courseId: courseId
            }
          }
        },
        orderBy: { order: 'asc' },
        include: {
          video: true
        }
      })

      return APIResponse.success({ lessons })
    } catch (error) {
      console.error('Error fetching lessons:', error)
      return APIResponse.error('Failed to fetch lessons', 500)
    }
  }
)
