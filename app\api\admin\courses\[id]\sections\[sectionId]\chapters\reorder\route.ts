import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-response'
import { withAuth } from '@/lib/auth-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const reorderSchema = z.object({
  chapters: z.array(z.object({
    id: z.string()
  }))
})

export const PUT = withAuth(
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      // Verify section exists
      const section = await prisma.courseSection.findUnique({
        where: {
          id: sectionId,
          courseId: courseId
        },
        select: { id: true }
      })

      if (!section) {
        return APIResponse.error('Section not found', 404)
      }

      // Update chapter timestamps to reflect new order
      const updatePromises = validatedBody.chapters.map((chapter: any, index: number) => {
        const timestamp = new Date(Date.now() + index * 1000)
        return prisma.courseChapter.update({
          where: { id: chapter.id },
          data: { updatedAt: timestamp }
        })
      })

      await Promise.all(updatePromises)

      return APIResponse.success({
        message: 'Chapters reordered successfully'
      })
    } catch (error) {
      console.error('Error reordering chapters:', error)
      return APIResponse.error('Failed to reorder chapters', 500)
    }
  },
  {
    requiredRole: 'ADMIN',
    bodySchema: reorderSchema
  }
)
