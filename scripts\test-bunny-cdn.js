#!/usr/bin/env node

/**
 * Bunny CDN Connection Test Script
 * 
 * This script tests the Bunny CDN configuration and connectivity
 * Run with: node scripts/test-bunny-cdn.js
 */

const { getBunnyStorage, getBunnyStorageWithTest } = require('../lib/bunny-storage')

async function testBunnyCDN() {
  console.log('🐰 Testing Bunny CDN Configuration...\n')

  // Test 1: Check environment variables
  console.log('1. Checking environment variables:')
  const requiredEnvVars = [
    'BUNNY_STORAGE_ZONE_NAME',
    'BUNNY_STORAGE_ACCESS_KEY', 
    'BUNNY_PULL_ZONE_URL',
    'BUNNY_STORAGE_REGION'
  ]

  let envVarsOk = true
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar]
    if (value) {
      console.log(`   ✅ ${envVar}: ${envVar.includes('KEY') ? '***' + value.slice(-4) : value}`)
    } else {
      console.log(`   ❌ ${envVar}: Not set`)
      envVarsOk = false
    }
  }

  if (!envVarsOk) {
    console.log('\n❌ Environment variables are not properly configured.')
    console.log('Please check your .env file and ensure all Bunny CDN variables are set.')
    return
  }

  console.log('\n2. Testing Bunny CDN connectivity:')
  
  try {
    // Test 2: Get storage instance
    const storage = await getBunnyStorageWithTest()
    console.log('   ✅ Storage instance created successfully')

    // Test 3: Test connection (if it's real Bunny storage)
    if (storage.testConnection) {
      console.log('   🔄 Testing connection to Bunny CDN...')
      const canConnect = await storage.testConnection()
      
      if (canConnect) {
        console.log('   ✅ Connection to Bunny CDN successful!')
      } else {
        console.log('   ⚠️  Cannot connect to Bunny CDN - using local fallback')
      }
    } else {
      console.log('   ⚠️  Using local storage fallback (Bunny CDN not configured)')
    }

    // Test 4: Test file upload with a small test file
    console.log('\n3. Testing file upload:')
    
    const testContent = 'This is a test file for Bunny CDN upload verification'
    const testBuffer = Buffer.from(testContent, 'utf8')
    
    console.log('   🔄 Uploading test file...')
    const uploadResult = await storage.uploadFile(testBuffer, {
      folder: 'test',
      filename: `test-${Date.now()}.txt`,
      contentType: 'text/plain'
    })

    if (uploadResult.success) {
      console.log('   ✅ Test file uploaded successfully!')
      console.log(`   📁 URL: ${uploadResult.url}`)
      console.log(`   📊 Size: ${uploadResult.size} bytes`)
      
      // Test 5: Test file deletion
      if (storage.deleteFile && uploadResult.url) {
        console.log('\n4. Testing file deletion:')
        console.log('   🔄 Deleting test file...')
        
        // Extract filepath from URL for deletion
        const filepath = uploadResult.url.replace(process.env.BUNNY_PULL_ZONE_URL + '/', '')
        const deleted = await storage.deleteFile(filepath)
        
        if (deleted) {
          console.log('   ✅ Test file deleted successfully!')
        } else {
          console.log('   ⚠️  Could not delete test file (this is normal for local fallback)')
        }
      }
    } else {
      console.log('   ❌ Test file upload failed:')
      console.log(`   Error: ${uploadResult.error}`)
    }

    console.log('\n🎉 Bunny CDN test completed!')
    
  } catch (error) {
    console.log('   ❌ Error during testing:')
    console.error('   ', error.message)
    
    if (error.message.includes('ENOTFOUND')) {
      console.log('\n💡 Troubleshooting tips:')
      console.log('   - Check your internet connection')
      console.log('   - Verify the Bunny CDN region is correct')
      console.log('   - Ensure the storage zone name exists')
      console.log('   - Verify the access key is valid')
    }
  }
}

// Run the test
if (require.main === module) {
  // Load environment variables
  require('dotenv').config()
  
  testBunnyCDN().catch(console.error)
}

module.exports = { testBunnyCDN }
