'use client'

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { ReactNode } from 'react'

interface SortableContainerProps {
  items: Array<{ id: string; order: number }>
  children: ReactNode
  onReorder: (items: Array<{ id: string; order: number }>) => void
  disabled?: boolean
}

export default function SortableContainer({
  items,
  children,
  onReorder,
  disabled = false
}: SortableContainerProps) {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (active.id !== over?.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id)
      const newIndex = items.findIndex((item) => item.id === over?.id)

      const reorderedItems = arrayMove(items, oldIndex, newIndex)
      
      // Update order values
      const updatedItems = reorderedItems.map((item, index) => ({
        ...item,
        order: index + 1
      }))

      onReorder(updatedItems)
    }
  }

  if (disabled) {
    return <div>{children}</div>
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext 
        items={items.map(item => item.id)} 
        strategy={verticalListSortingStrategy}
      >
        {children}
      </SortableContext>
    </DndContext>
  )
}
