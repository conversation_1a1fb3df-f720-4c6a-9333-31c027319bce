import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { getBunnyStorage } from '@/lib/bunny-storage'
import { z } from 'zod'

const generateCertificateSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required')
})

// GET /api/student/courses/certificates - Get user's certificates
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      const url = new URL(request.url)
      const courseId = url.searchParams.get('courseId')

      if (courseId) {
        // Get certificate for specific course
        const certificate = await prisma.courseCertificate.findUnique({
          where: {
            courseId_userId: {
              courseId,
              userId: user.id
            }
          },
          include: {
            course: {
              select: {
                id: true,
                title: true,
                instructor: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        })

        if (!certificate) {
          return APIResponse.error('Certificate not found', 404)
        }

        return APIResponse.success({ certificate })
      }

      // Get all certificates for user
      const certificates = await prisma.courseCertificate.findMany({
        where: { userId: user.id },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              thumbnailImage: true,
              instructor: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: { issuedAt: 'desc' }
      })

      return APIResponse.success({ certificates })
    } catch (error) {
      console.error('Error fetching certificates:', error)
      return APIResponse.error('Failed to fetch certificates', 500)
    }
  }
)

// POST /api/student/courses/certificates - Generate certificate for completed course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: generateCertificateSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { courseId } = validatedBody

      // Check if user is enrolled and has completed the course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        },
        include: {
          course: {
            include: {
              instructor: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('You are not enrolled in this course', 404)
      }

      if (enrollment.status !== 'completed' || enrollment.progress < 100) {
        return APIResponse.error('Course must be completed to generate certificate', 400)
      }

      // Check if certificate already exists
      const existingCertificate = await prisma.courseCertificate.findUnique({
        where: {
          courseId_userId: {
            courseId,
            userId: user.id
          }
        }
      })

      if (existingCertificate) {
        return APIResponse.success({
          message: 'Certificate already exists',
          certificate: existingCertificate
        })
      }

      // Get user details
      const userDetails = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          name: true,
          email: true
        }
      })

      if (!userDetails) {
        return APIResponse.error('User not found', 404)
      }

      // Calculate final score (average of all quiz attempts)
      const quizAttempts = await prisma.courseQuizAttempt.findMany({
        where: {
          userId: user.id,
          quiz: {
            courseId
          }
        },
        select: {
          percentage: true
        }
      })

      const finalScore = quizAttempts.length > 0
        ? Math.round(quizAttempts.reduce((acc, attempt) => acc + attempt.percentage, 0) / quizAttempts.length)
        : null

      // Generate unique certificate ID
      const certificateId = `CERT-${courseId.slice(-8).toUpperCase()}-${user.id.slice(-8).toUpperCase()}-${Date.now().toString(36).toUpperCase()}`

      // Generate certificate PDF (this would typically use a PDF generation library)
      const certificateData = {
        certificateId,
        studentName: userDetails.name,
        courseName: enrollment.course.title,
        instructorName: enrollment.course.instructor.name,
        completionDate: enrollment.completedAt!,
        issuedDate: new Date(),
        finalScore
      }

      // For now, we'll create a simple certificate record without PDF generation
      // In a real implementation, you would use libraries like puppeteer, jsPDF, or PDFKit
      const pdfUrl = await generateCertificatePDF(certificateData)

      // Create certificate record
      const certificate = await prisma.courseCertificate.create({
        data: {
          courseId,
          userId: user.id,
          certificateId,
          completionDate: enrollment.completedAt!,
          finalScore,
          pdfUrl
        },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              instructor: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Certificate generated successfully',
        certificate
      })
    } catch (error) {
      console.error('Error generating certificate:', error)
      return APIResponse.error('Failed to generate certificate', 500)
    }
  }
)

// Helper function to generate certificate PDF
async function generateCertificatePDF(data: {
  certificateId: string
  studentName: string
  courseName: string
  instructorName: string
  completionDate: Date
  issuedDate: Date
  finalScore: number | null
}): Promise<string> {
  try {
    // This is a placeholder implementation
    // In a real application, you would use a PDF generation library
    
    // For now, return a placeholder URL
    // In production, this would:
    // 1. Generate a PDF using puppeteer, jsPDF, or similar
    // 2. Upload the PDF to Bunny CDN
    // 3. Return the CDN URL
    
    const bunnyStorage = getBunnyStorage()
    
    // Create a simple HTML template for the certificate
    const certificateHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Times New Roman', serif; text-align: center; padding: 50px; }
          .certificate { border: 10px solid #0066cc; padding: 50px; margin: 20px; }
          .title { font-size: 48px; color: #0066cc; margin-bottom: 30px; }
          .subtitle { font-size: 24px; margin-bottom: 40px; }
          .student-name { font-size: 36px; font-weight: bold; color: #333; margin: 30px 0; }
          .course-name { font-size: 28px; font-style: italic; margin: 20px 0; }
          .details { font-size: 18px; margin: 20px 0; }
          .signature { margin-top: 60px; }
        </style>
      </head>
      <body>
        <div class="certificate">
          <div class="title">CERTIFICATE OF COMPLETION</div>
          <div class="subtitle">This is to certify that</div>
          <div class="student-name">${data.studentName}</div>
          <div class="subtitle">has successfully completed the course</div>
          <div class="course-name">${data.courseName}</div>
          <div class="details">
            Completion Date: ${data.completionDate.toLocaleDateString()}<br>
            ${data.finalScore ? `Final Score: ${data.finalScore}%<br>` : ''}
            Certificate ID: ${data.certificateId}
          </div>
          <div class="signature">
            <div style="margin-top: 40px; border-top: 1px solid #333; width: 200px; margin: 40px auto 10px;">
              ${data.instructorName}
            </div>
            <div>Instructor</div>
          </div>
        </div>
      </body>
      </html>
    `

    // In a real implementation, you would convert this HTML to PDF
    // and upload it to Bunny CDN. For now, we'll return a placeholder URL
    const filename = `certificate-${data.certificateId}.pdf`
    
    // This would be the actual PDF upload in production:
    // const pdfBuffer = await generatePDFFromHTML(certificateHTML)
    // const uploadResult = await bunnyStorage.uploadFile(pdfBuffer, {
    //   folder: 'certificates',
    //   filename,
    //   contentType: 'application/pdf'
    // })
    
    // For now, return a placeholder URL
    return `https://cdn.example.com/certificates/${filename}`
  } catch (error) {
    console.error('Error generating certificate PDF:', error)
    throw new Error('Failed to generate certificate PDF')
  }
}
