'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CourseCard, type Course } from './course-card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { BookOpen, ChevronLeft, ChevronRight } from 'lucide-react'

interface CourseGridProps {
  courses: Course[]
  isLoading?: boolean
  onEnroll?: (courseId: string) => void
  onViewDetails?: (courseId: string) => void
  onToggleFavorite?: (courseId: string) => void
  onShare?: (course: Course) => void
  favorites?: Set<string>
  pagination?: {
    page: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  onPageChange?: (page: number) => void
  className?: string
}

export function CourseGrid({
  courses,
  isLoading = false,
  onEnroll,
  onViewDetails,
  onToggleFavorite,
  onShare,
  favorites = new Set(),
  pagination,
  onPageChange,
  className
}: CourseGridProps) {
  if (isLoading) {
    return (
      <div className={className}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <CourseCardSkeleton key={index} />
          ))}
        </div>
      </div>
    )
  }

  if (courses.length === 0) {
    return (
      <div className={className}>
        <EmptyState />
      </div>
    )
  }

  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        <motion.div
          key={pagination?.page || 'courses'}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {courses.map((course, index) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
            >
              <CourseCard
                course={course}
                onEnroll={onEnroll}
                onViewDetails={onViewDetails}
                onToggleFavorite={onToggleFavorite}
                onShare={onShare}
                isFavorite={favorites.has(course.id)}
              />
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="flex items-center justify-center gap-2 mt-8"
        >
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(pagination.page - 1)}
            disabled={!pagination.hasPrev}
            className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Previous
          </Button>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              let pageNum: number
              if (pagination.totalPages <= 5) {
                pageNum = i + 1
              } else if (pagination.page <= 3) {
                pageNum = i + 1
              } else if (pagination.page >= pagination.totalPages - 2) {
                pageNum = pagination.totalPages - 4 + i
              } else {
                pageNum = pagination.page - 2 + i
              }

              return (
                <Button
                  key={pageNum}
                  variant={pageNum === pagination.page ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange?.(pageNum)}
                  className={
                    pageNum === pagination.page
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                      : "bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm"
                  }
                >
                  {pageNum}
                </Button>
              )
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(pagination.page + 1)}
            disabled={!pagination.hasNext}
            className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm"
          >
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </motion.div>
      )}
    </div>
  )
}

function CourseCardSkeleton() {
  return (
    <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-xl border shadow-lg overflow-hidden">
      <Skeleton className="h-48 w-full" />
      <div className="p-6 space-y-4">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
        <div className="flex gap-2">
          <Skeleton className="h-6 w-16" />
          <Skeleton className="h-6 w-16" />
          <Skeleton className="h-6 w-16" />
        </div>
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
    </div>
  )
}

function EmptyState() {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col items-center justify-center py-16 text-center"
    >
      <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full flex items-center justify-center mb-6">
        <BookOpen className="h-12 w-12 text-blue-500" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        No courses found
      </h3>
      <p className="text-gray-600 dark:text-gray-400 max-w-md">
        We couldn't find any courses matching your criteria. Try adjusting your filters, search terms, or check back later for new courses.
      </p>
    </motion.div>
  )
}
