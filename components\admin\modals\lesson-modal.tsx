'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { Tab } from '@headlessui/react'
import { 
  PlayIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ClipboardDocumentListIcon,
  DocumentIcon,
  FolderIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline'
import ContentModal from '../content-modal'
import VideoUpload from '../video-upload'
import FileManager from '../file-manager'
import QuizSelector from '../quiz-selector'

interface LessonFormData {
  title: string
  description: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  duration: number
  isPublished: boolean
  isFree: boolean
  content: string
  videoUrl: string
  attachments: any[]
  hasQuiz: boolean
  quizId?: string
}

interface LessonModalProps {
  isOpen: boolean
  onClose: () => void
  courseId: string
  sectionId: string
  chapterId: string
  onSuccess: () => void
  editingLesson?: {
    id: string
    title: string
    description: string
    type: string
    order: number
    duration: number
    isPublished: boolean
    isFree: boolean
    content: string
    videoUrl: string
    attachments: any[]
    hasQuiz: boolean
    quizId?: string
    video?: {
      id: string
      url: string
      filename: string
      originalName: string
      duration?: number
      size: number
      thumbnailUrl?: string
    }
  }
}

export default function LessonModal({
  isOpen,
  onClose,
  courseId,
  sectionId,
  chapterId,
  onSuccess,
  editingLesson
}: LessonModalProps) {
  const [loading, setLoading] = useState(false)
  const [createdLessonId, setCreatedLessonId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState(0)
  const [selectedQuiz, setSelectedQuiz] = useState<any>(null)
  const [formData, setFormData] = useState<LessonFormData>({
    title: editingLesson?.title || '',
    description: editingLesson?.description || '',
    type: (editingLesson?.type as any) || 'VIDEO',

    duration: editingLesson?.duration || 0,
    isPublished: editingLesson?.isPublished || false,
    isFree: editingLesson?.isFree || false,
    content: editingLesson?.content || '',
    videoUrl: editingLesson?.videoUrl || '',
    attachments: editingLesson?.attachments || [],
    hasQuiz: editingLesson?.hasQuiz || false,
    quizId: editingLesson?.quizId
  })

  // Update form data when editingLesson changes
  useEffect(() => {
    if (editingLesson) {
      setFormData({
        title: editingLesson.title || '',
        description: editingLesson.description || '',
        type: (editingLesson.type as any) || 'VIDEO',

        duration: editingLesson.duration || 0,
        isPublished: editingLesson.isPublished || false,
        isFree: editingLesson.isFree || false,
        content: editingLesson.content || '',
        videoUrl: editingLesson.videoUrl || '',
        attachments: editingLesson.attachments || [],
        hasQuiz: editingLesson.hasQuiz || false,
        quizId: editingLesson.quizId
      })
    } else {
      setFormData({
        title: '',
        description: '',
        type: 'VIDEO',

        duration: 0,
        isPublished: false,
        isFree: false,
        content: '',
        videoUrl: '',
        attachments: [],
        hasQuiz: false,
        quizId: undefined
      })
    }
  }, [editingLesson])

  const lessonTypes = [
    { 
      value: 'VIDEO', 
      label: 'Video Lesson', 
      icon: PlayIcon,
      description: 'Upload and stream video content'
    },
    { 
      value: 'TEXT', 
      label: 'Text Content', 
      icon: DocumentTextIcon,
      description: 'Rich text content and articles'
    },
    { 
      value: 'QUIZ', 
      label: 'Quiz', 
      icon: AcademicCapIcon,
      description: 'Interactive quizzes and assessments'
    },
    { 
      value: 'ASSIGNMENT', 
      label: 'Assignment', 
      icon: ClipboardDocumentListIcon,
      description: 'Homework and practical exercises'
    },
    { 
      value: 'DOCUMENT', 
      label: 'Document', 
      icon: DocumentIcon,
      description: 'PDFs, slides, and downloadable files'
    }
  ]

  const tabs = [
    { name: 'Content', icon: DocumentTextIcon },
    { name: 'Files', icon: FolderIcon },
    { name: 'Quiz', icon: QuestionMarkCircleIcon }
  ]

  const handleInputChange = (field: keyof LessonFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleVideoUploadComplete = (video: any) => {
    setFormData(prev => ({
      ...prev,
      videoUrl: video.url,
      // Only update duration if it's currently 0 (not manually set)
      duration: prev.duration === 0 ? (video.duration || 0) : prev.duration
    }))
    toast.success('Video uploaded successfully!')
  }

  const handleFilesChange = (files: any[]) => {
    setFormData(prev => ({ ...prev, attachments: files }))
  }

  const handleQuizSelect = (quiz: any) => {
    setSelectedQuiz(quiz)
    setFormData(prev => ({
      ...prev,
      hasQuiz: !!quiz,
      quizId: quiz?.id || undefined
    }))
  }

  const handleCreateNewQuiz = () => {
    // Open quiz creation in new tab/window
    window.open('/admin/quizzes/create', '_blank')
    toast('Quiz creator opened in new tab. Refresh this page after creating your quiz.', {
      icon: 'ℹ️',
      duration: 4000
    })
  }

  const validateForm = () => {
    if (!formData.title.trim()) {
      toast.error('Lesson title is required')
      return false
    }

    if ((formData.type === 'TEXT' || formData.type === 'DOCUMENT') && !formData.content.trim()) {
      toast.error(`Please add content for ${formData.type.toLowerCase()} lessons`)
      return false
    }

    if (formData.type === 'QUIZ' && !formData.content.trim()) {
      toast.error('Please add quiz questions and answers')
      return false
    }

    if (formData.type === 'ASSIGNMENT' && !formData.content.trim()) {
      toast.error('Please add assignment instructions')
      return false
    }

    return true
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      setLoading(true)
      
      const url = editingLesson 
        ? `/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons/${editingLesson.id}`
        : `/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons`
      
      const method = editingLesson ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || `Failed to ${editingLesson ? 'update' : 'create'} lesson`)
      }

      const result = await response.json()
      const data = result.data || result
      const lesson = data.lesson || data
      
      toast.success(data.message || `Lesson ${editingLesson ? 'updated' : 'created'} successfully!`)
      
      if (!editingLesson && lesson?.id) {
        setCreatedLessonId(lesson.id)
      }
      
      onSuccess()
      onClose()

    } catch (error: any) {
      console.error(`Error ${editingLesson ? 'updating' : 'creating'} lesson:`, error)
      toast.error(error.message || `Failed to ${editingLesson ? 'update' : 'create'} lesson`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!editingLesson) {
      setFormData({
        title: '',
        description: '',
        type: 'VIDEO',

        duration: 0,
        isPublished: false,
        isFree: false,
        content: '',
        videoUrl: '',
        attachments: [],
        hasQuiz: false
      })
      setActiveTab(0)
    }
    onClose()
  }

  return (
    <ContentModal
      isOpen={isOpen}
      onClose={handleClose}
      title={editingLesson ? 'Edit Lesson' : 'Add New Lesson'}
      description={editingLesson ? 'Update lesson content and settings' : 'Create a new lesson with content, files, and optional quiz'}
      size="2xl"
    >
      <div className="space-y-4 lg:space-y-6">
        {/* Basic Information */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Lesson Title *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className="w-full px-3 lg:px-4 py-2 lg:py-3 bg-white/50 dark:bg-slate-700/50 border border-gray-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm lg:text-base"
            placeholder="Enter lesson title"
            required
          />
        </div>

        {/* Duration and Settings */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Duration (minutes)
            </label>
            <input
              type="number"
              min="0"
              step="0.5"
              value={Math.round(formData.duration / 60 * 10) / 10}
              onChange={(e) => handleInputChange('duration', Math.round(parseFloat(e.target.value) * 60) || 0)}
              className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              placeholder="0"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {formData.type === 'VIDEO' ? 'Auto-detected from video upload, or set manually' : 'Estimated time to complete this lesson'}
            </p>
          </div>
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isFree}
                onChange={(e) => handleInputChange('isFree', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Free Preview</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isPublished}
                onChange={(e) => handleInputChange('isPublished', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Published</span>
            </label>
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
            className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
            placeholder="Describe what this lesson covers"
          />
        </div>

        {/* Lesson Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Lesson Type *
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 lg:gap-3">
            {lessonTypes.map((type) => {
              const Icon = type.icon
              return (
                <motion.button
                  key={type.value}
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleInputChange('type', type.value)}
                  className={`p-2 lg:p-3 border-2 rounded-xl transition-all duration-200 text-center ${
                    formData.type === type.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                      : 'border-gray-200 dark:border-slate-600 bg-white/50 dark:bg-slate-700/50 text-gray-600 dark:text-gray-300 hover:border-gray-300 dark:hover:border-slate-500'
                  }`}
                >
                  <Icon className="w-4 lg:w-5 h-4 lg:h-5 mx-auto mb-1 lg:mb-2" />
                  <div className="text-xs font-medium">{type.label}</div>
                </motion.button>
              )
            })}
          </div>
        </div>

        {/* Tabbed Content */}
        <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
          <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 dark:bg-slate-700/50 p-1">
            {tabs.map((tab, index) => {
              const Icon = tab.icon
              return (
                <Tab
                  key={tab.name}
                  className={({ selected }) =>
                    `w-full rounded-lg py-2 lg:py-2.5 text-xs lg:text-sm font-medium leading-5 transition-all duration-200 ${
                      selected
                        ? 'bg-white dark:bg-slate-600 text-blue-700 dark:text-blue-300 shadow'
                        : 'text-blue-100 dark:text-slate-300 hover:bg-white/[0.12] dark:hover:bg-slate-600/50 hover:text-white'
                    }`
                  }
                >
                  <div className="flex items-center justify-center space-x-1 lg:space-x-2">
                    <Icon className="w-3 lg:w-4 h-3 lg:h-4" />
                    <span className="hidden sm:inline">{tab.name}</span>
                  </div>
                </Tab>
              )
            })}
          </Tab.List>
          
          <Tab.Panels className="mt-6">
            {/* Content Tab */}
            <Tab.Panel className="space-y-6">
              {/* Video Upload for VIDEO type */}
              {formData.type === 'VIDEO' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Video Upload
                  </label>
                  <VideoUpload
                    lessonId={createdLessonId || editingLesson?.id}
                    existingVideo={editingLesson?.video ? {
                      id: editingLesson.video.id,
                      url: editingLesson.video.url,
                      filename: editingLesson.video.filename,
                      originalName: editingLesson.video.originalName,
                      duration: editingLesson.video.duration,
                      size: editingLesson.video.size,
                      thumbnail: editingLesson.video.thumbnailUrl
                    } : undefined}
                    onUploadComplete={handleVideoUploadComplete}
                    onUploadError={(error) => toast.error(error)}
                    disabled={!createdLessonId && !editingLesson?.id}
                  />
                </div>
              )}

              {/* Content for non-video lessons */}
              {formData.type !== 'VIDEO' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {formData.type === 'TEXT' && 'Text Content *'}
                    {formData.type === 'QUIZ' && 'Quiz Questions & Answers *'}
                    {formData.type === 'ASSIGNMENT' && 'Assignment Instructions *'}
                    {formData.type === 'DOCUMENT' && 'Document Description *'}
                  </label>
                  <textarea
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    rows={formData.type === 'QUIZ' ? 12 : 8}
                    className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                    placeholder={
                      formData.type === 'TEXT' ? 'Write your lesson content using markdown or plain text...' :
                      formData.type === 'QUIZ' ? 'Add your quiz questions and multiple choice answers...' :
                      formData.type === 'ASSIGNMENT' ? 'Provide clear instructions for the assignment...' :
                      formData.type === 'DOCUMENT' ? 'Describe the document and provide download links...' :
                      'Enter lesson content...'
                    }
                  />
                </div>
              )}
            </Tab.Panel>

            {/* Files Tab */}
            <Tab.Panel>
              <FileManager
                lessonId={createdLessonId || editingLesson?.id}
                courseId={courseId}
                existingFiles={formData.attachments}
                onFilesChange={handleFilesChange}
              />
            </Tab.Panel>

            {/* Quiz Tab */}
            <Tab.Panel>
              <div className="space-y-4 lg:space-y-6">
                <div className="text-center">
                  <QuestionMarkCircleIcon className="w-10 lg:w-12 h-10 lg:h-12 text-blue-600 dark:text-blue-400 mx-auto mb-3" />
                  <h3 className="text-base lg:text-lg font-medium text-gray-800 dark:text-white mb-2">Quiz Management</h3>
                  <p className="text-sm lg:text-base text-gray-600 dark:text-gray-300 mb-4 lg:mb-6">
                    Associate a quiz with this lesson to test student understanding
                  </p>
                </div>

                <QuizSelector
                  courseId={courseId}
                  selectedQuizId={formData.quizId}
                  onQuizSelect={handleQuizSelect}
                  onCreateNew={handleCreateNewQuiz}
                />

                {/* Quiz Settings */}
                {selectedQuiz && (
                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <h4 className="font-medium text-blue-800 mb-3">Quiz Settings</h4>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          id="quizRequired"
                          checked={formData.hasQuiz}
                          onChange={(e) => handleInputChange('hasQuiz', e.target.checked)}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <label htmlFor="quizRequired" className="text-sm font-medium text-blue-700">
                          Quiz is required to complete this lesson
                        </label>
                      </div>
                      <div className="text-sm text-blue-600">
                        <p><strong>Questions:</strong> {selectedQuiz.totalQuestions}</p>
                        <p><strong>Time Limit:</strong> {selectedQuiz.timeLimit} minutes</p>
                        <p><strong>Difficulty:</strong> {selectedQuiz.difficulty}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>

        {/* Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="isPublished"
              checked={formData.isPublished}
              onChange={(e) => handleInputChange('isPublished', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            />
            <label htmlFor="isPublished" className="text-sm font-medium text-gray-700">
              {editingLesson ? 'Lesson is published' : 'Publish lesson immediately'}
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="isFree"
              checked={formData.isFree}
              onChange={(e) => handleInputChange('isFree', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            />
            <label htmlFor="isFree" className="text-sm font-medium text-gray-700">
              Make this lesson free (preview)
            </label>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <motion.button
            type="button"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleClose}
            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
          >
            Cancel
          </motion.button>
          <motion.button
            type="button"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleSubmit}
            disabled={loading}
            className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
          >
            {loading ? (editingLesson ? 'Updating...' : 'Creating...') : (editingLesson ? 'Update Lesson' : 'Create Lesson')}
          </motion.button>
        </div>
      </div>
    </ContentModal>
  )
}
