import { NextRequest, NextResponse } from 'next/server'
 import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/categories/subjects - Get all subjects for students
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
  },
  async (request: NextRequest, { user }) => {
    try {
      const subjects = await prisma.subject.findMany({
        where: {
          isActive: true
        },
        include: {
          chapters: {
            where: {
              isActive: true
            },
            include: {
              topics: {
                where: {
                  isActive: true
                },
                orderBy: { createdAt: 'asc' }
              }
            },
            orderBy: { createdAt: 'asc' }
          },
          _count: {
            select: {
              quizzes: {
                where: { isPublished: true }
              },
              chapters: {
                where: { isActive: true }
              }
            }
          }
        },
        orderBy: { name: 'asc' }
      })

      // Return all active subjects, chapters, and topics
      // Students can see all categories even if no quizzes exist yet
      let filteredSubjects = subjects

      // If no subjects exist, create some default ones for better UX
      if (filteredSubjects.length === 0) {
        console.log('No subjects found, creating default subjects for students')

        // Create default subjects
        const defaultSubjects = [
          { name: 'Mathematics', description: 'Mathematical concepts and problem solving' },
          { name: 'Science', description: 'Scientific principles and theories' },
          { name: 'Programming', description: 'Computer programming and software development' },
          { name: 'General Knowledge', description: 'General knowledge and current affairs' }
        ]

        for (const subjectData of defaultSubjects) {
          try {
            await prisma.subject.create({
              data: subjectData
            })
          } catch (error) {
            // Subject might already exist, ignore error
            console.log(`Subject ${subjectData.name} might already exist`)
          }
        }

        // Fetch subjects again after creating defaults
        filteredSubjects = await prisma.subject.findMany({
          where: { isActive: true },
          include: {
            chapters: {
              where: { isActive: true },
              include: {
                topics: {
                  where: { isActive: true },
                  orderBy: { createdAt: 'asc' }
                }
              },
              orderBy: { createdAt: 'asc' }
            },
            _count: {
              select: {
                quizzes: { where: { isPublished: true } },
                chapters: { where: { isActive: true } }
              }
            }
          },
          orderBy: { name: 'asc' }
        })
      }

      return APIResponse.success({
        subjects: filteredSubjects
      }, 'Subjects retrieved successfully')

    } catch (error) {
      console.error('Error fetching subjects for student:', error)
      return APIResponse.error('Failed to fetch subjects', 500)
    }
  }
)
