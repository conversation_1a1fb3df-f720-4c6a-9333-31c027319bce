import { NextRequest, NextResponse } from 'next/server';
import { APIResponse, createAPIHandler } from '@/lib/api-middleware';
import { prisma } from '@/lib/prisma';

interface SystemSettingsData {
  companyName: string;
  companyDescription: string;
  companyMission: string;
  companyVision: string;
  contactEmail: string;
  contactPhone: string;
  contactAddress: string;
  supportEmail: string;
  privacyEmail: string;
  legalEmail: string;
  youtubeChannel?: string;
  facebookPage?: string;
  twitterHandle?: string;
  instagramHandle?: string;
  linkedinPage?: string;
  logoUrl?: string;
  faviconUrl?: string;
  brandColor: string;
  siteUrl: string;
  timezone: string;
  language: string;
  businessHours: string;
  teamMembers: any[];
  totalStudents: number;
  successRate: number;
  coursesOffered: number;
}

// Helper function to get or create system settings
async function getSystemSettings() {
  const defaultSettings = {
    companyName: 'PrepLocus',
    companyDescription: 'India\'s leading online coaching platform',
    companyMission: 'Empowering students to achieve their dreams through quality education',
    companyVision: 'To be the most trusted educational platform in India',
    contactEmail: '<EMAIL>',
    contactPhone: '+91 98765 43210',
    contactAddress: 'New Delhi, India',
    supportEmail: '<EMAIL>',
    privacyEmail: '<EMAIL>',
    legalEmail: '<EMAIL>',
    youtubeChannel: 'https://www.youtube.com/@YourChannelName',
    facebookPage: '',
    twitterHandle: '',
    instagramHandle: '',
    linkedinPage: '',
    logoUrl: '',
    faviconUrl: '',
    brandColor: '#8B5CF6',
    siteUrl: 'http://localhost:3000',
    timezone: 'Asia/Kolkata',
    language: 'en',
    businessHours: 'Mon-Fri, 9 AM - 6 PM IST',
    teamMembers: [
      {
        id: '1',
        name: 'Rajesh Kumar',
        title: 'Founder & CEO',
        bio: 'Former IIT graduate with 15+ years in education technology. Passionate about making quality education accessible to every student in India.',
        image: '/images/team/founder.jpg',
        expertise: ['Education Technology', 'Strategic Planning', 'Team Leadership']
      },
      {
        id: '2',
        name: 'Priya Sharma',
        title: 'Co-Founder & CTO',
        bio: 'Ex-Google engineer with expertise in AI and machine learning. Leading the technical innovation at PrepLocus to create personalized learning experiences.',
        image: '/images/team/cofounder.jpg',
        expertise: ['Artificial Intelligence', 'Software Architecture', 'Product Development']
      }
    ],
    totalStudents: 500000,
    successRate: 98,
    coursesOffered: 50
  };

  // Try to get existing settings using the key-value approach
  const settings = await prisma.systemSetting.findMany({
    where: {
      category: 'company'
    }
  });

  if (settings.length === 0) {
    // Create default settings
    const settingsToCreate = Object.entries(defaultSettings).map(([key, value]) => ({
      key: `company.${key}`,
      value: typeof value === 'object' ? JSON.stringify(value) : String(value),
      category: 'company',
      description: `Company ${key}`
    }));

    await prisma.systemSetting.createMany({
      data: settingsToCreate
    });

    return defaultSettings;
  }

  // Convert key-value pairs back to object
  const result: any = {};
  settings.forEach(setting => {
    const key = setting.key.replace('company.', '');
    try {
      result[key] = setting.key === 'company.teamMembers' ? JSON.parse(setting.value) : setting.value;
    } catch {
      result[key] = setting.value;
    }
  });

  return { ...defaultSettings, ...result };
}

// GET /api/admin/system-settings - Get system settings
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const settings = await getSystemSettings();
      
      return APIResponse.success({
        settings,
        message: 'System settings retrieved successfully'
      });
    } catch (error: any) {
      console.error('Error fetching system settings:', error);
      return APIResponse.error('Failed to fetch system settings: ' + error.message, 500);
    }
  }
);

// PUT /api/admin/system-settings - Update system settings
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const data: SystemSettingsData = await request.json();

      // Validate required fields
      if (!data.companyName || !data.contactEmail) {
        return APIResponse.error('Company name and contact email are required', 400);
      }

      // Update settings using key-value approach
      const settingsToUpdate = Object.entries(data).map(([key, value]) => ({
        key: `company.${key}`,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        category: 'company',
        description: `Company ${key}`
      }));

      // Delete existing company settings and create new ones
      await prisma.systemSetting.deleteMany({
        where: { category: 'company' }
      });

      await prisma.systemSetting.createMany({
        data: settingsToUpdate
      });

      return APIResponse.success({
        message: 'System settings updated successfully',
        settings: data
      });
    } catch (error: any) {
      console.error('Error updating system settings:', error);
      return APIResponse.error('Failed to update system settings: ' + error.message, 500);
    }
  }
);

// POST /api/admin/system-settings/reset - Reset to default settings
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      // Delete all company settings
      await prisma.systemSetting.deleteMany({
        where: { category: 'company' }
      });

      // Get default settings (this will create them)
      const settings = await getSystemSettings();

      return APIResponse.success({
        message: 'System settings reset to defaults successfully',
        settings
      });
    } catch (error: any) {
      console.error('Error resetting system settings:', error);
      return APIResponse.error('Failed to reset system settings: ' + error.message, 500);
    }
  }
);
