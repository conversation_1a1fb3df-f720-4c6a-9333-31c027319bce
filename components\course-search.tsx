'use client'

import { useState, useEffect, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
  StarIcon,
  ClockIcon,
  AcademicCapIcon,
  UsersIcon
} from '@heroicons/react/24/outline'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'

interface Course {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  slug?: string
  category?: string
  level?: string
  thumbnailImage?: string
  rating?: number
  studentsCount?: number
  totalLessons: number
  totalDuration: number
  isEnrolled: boolean
  instructor: {
    id: string
    name: string
    image?: string
  }
}

interface SearchFilters {
  search: string
  category: string
  level: string
  priceRange: string
  rating: string
  duration: string
  sortBy: string
}

interface CourseSearchProps {
  initialCourses?: Course[]
  showFilters?: boolean
  className?: string
}

const categories = [
  'Programming', 'Design', 'Business', 'Marketing', 
  'Photography', 'Music', 'Writing', 'Health & Fitness'
]

const levels = ['Beginner', 'Intermediate', 'Advanced']

const priceRanges = [
  { label: 'Free', value: 'free' },
  { label: 'Under $50', value: '0-50' },
  { label: '$50 - $100', value: '50-100' },
  { label: '$100 - $200', value: '100-200' },
  { label: 'Over $200', value: '200+' }
]

const sortOptions = [
  { label: 'Most Popular', value: 'popular' },
  { label: 'Highest Rated', value: 'rating' },
  { label: 'Newest', value: 'newest' },
  { label: 'Price: Low to High', value: 'price-asc' },
  { label: 'Price: High to Low', value: 'price-desc' }
]

export default function CourseSearch({ initialCourses = [], showFilters = true, className = '' }: CourseSearchProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [courses, setCourses] = useState<Course[]>(initialCourses)
  const [loading, setLoading] = useState(false)
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  
  const [filters, setFilters] = useState<SearchFilters>({
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    level: searchParams.get('level') || '',
    priceRange: searchParams.get('priceRange') || '',
    rating: searchParams.get('rating') || '',
    duration: searchParams.get('duration') || '',
    sortBy: searchParams.get('sortBy') || 'popular'
  })

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (filters.search !== (searchParams.get('search') || '')) {
        updateURL()
      }
    }, 500)

    return () => clearTimeout(timer)
  }, [filters.search])

  // Update URL when filters change
  useEffect(() => {
    updateURL()
  }, [filters.category, filters.level, filters.priceRange, filters.rating, filters.duration, filters.sortBy])

  const updateURL = () => {
    const params = new URLSearchParams()
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value)
      }
    })

    const newURL = `${window.location.pathname}?${params.toString()}`
    router.push(newURL, { scroll: false })
    
    // Fetch courses with new filters
    fetchCourses()
  }

  const fetchCourses = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          if (key === 'search') params.set('search', value)
          else if (key === 'category') params.set('category', value)
          else if (key === 'level') params.set('level', value)
          else if (key === 'priceRange') {
            if (value === 'free') {
              params.set('maxPrice', '0')
            } else if (value.includes('-')) {
              const [min, max] = value.split('-')
              params.set('minPrice', min)
              if (max !== '+') params.set('maxPrice', max)
            } else if (value.endsWith('+')) {
              params.set('minPrice', value.replace('+', ''))
            }
          }
          else if (key === 'sortBy') {
            // Handle sorting in the API
            params.set('sortBy', value)
          }
        }
      })

      const response = await fetch(`/api/courses?${params.toString()}`)
      if (!response.ok) throw new Error('Failed to fetch courses')

      const data = await response.json()
      setCourses(data.courses)
    } catch (error) {
      console.error('Error fetching courses:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateFilter = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      level: '',
      priceRange: '',
      rating: '',
      duration: '',
      sortBy: 'popular'
    })
  }

  const activeFilterCount = useMemo(() => {
    return Object.entries(filters).filter(([key, value]) => 
      value && key !== 'search' && key !== 'sortBy'
    ).length
  }, [filters])



  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search Bar */}
      <div className="relative">
        <MagnifyingGlassIcon className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <input
          type="text"
          placeholder="Search courses..."
          value={filters.search}
          onChange={(e) => updateFilter('search', e.target.value)}
          className="w-full pl-12 pr-4 py-4 bg-white/60 backdrop-blur-xl border border-white/20 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-lg"
        />
      </div>

      {/* Filters */}
      {showFilters && (
        <>
          {/* Desktop Filters */}
          <div className="hidden lg:block">
            <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800">Filters</h3>
                {activeFilterCount > 0 && (
                  <button
                    onClick={clearFilters}
                    className="text-sm text-blue-600 hover:text-blue-700 transition-colors duration-200"
                  >
                    Clear all ({activeFilterCount})
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={filters.category}
                    onChange={(e) => updateFilter('category', e.target.value)}
                    className="w-full px-3 py-2 bg-white/50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  >
                    <option value="">All Categories</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                {/* Level */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
                  <select
                    value={filters.level}
                    onChange={(e) => updateFilter('level', e.target.value)}
                    className="w-full px-3 py-2 bg-white/50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  >
                    <option value="">All Levels</option>
                    {levels.map(level => (
                      <option key={level} value={level}>{level}</option>
                    ))}
                  </select>
                </div>

                {/* Price Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Price</label>
                  <select
                    value={filters.priceRange}
                    onChange={(e) => updateFilter('priceRange', e.target.value)}
                    className="w-full px-3 py-2 bg-white/50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  >
                    <option value="">All Prices</option>
                    {priceRanges.map(range => (
                      <option key={range.value} value={range.value}>{range.label}</option>
                    ))}
                  </select>
                </div>

                {/* Sort By */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => updateFilter('sortBy', e.target.value)}
                    className="w-full px-3 py-2 bg-white/50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  >
                    {sortOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Filter Button */}
          <div className="lg:hidden flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {courses.length} courses found
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setShowMobileFilters(true)}
              className="flex items-center px-4 py-2 bg-white/60 backdrop-blur-xl border border-white/20 rounded-xl hover:bg-white/80 transition-all duration-200"
            >
              <AdjustmentsHorizontalIcon className="w-4 h-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <span className="ml-2 px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                  {activeFilterCount}
                </span>
              )}
            </motion.button>
          </div>
        </>
      )}

      {/* Results */}
      <div className="space-y-4">
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6 animate-pulse">
                <div className="h-48 bg-gray-200 rounded-xl mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        ) : courses.length === 0 ? (
          <div className="text-center py-12">
            <AcademicCapIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No courses found</h3>
            <p className="text-gray-500 mb-6">Try adjusting your search criteria</p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={clearFilters}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Clear Filters
            </motion.button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {courses.map((course, index) => (
                <CourseCard key={course.id} course={course?.title || "Unknown Course"} index={index} />
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Mobile Filters Modal */}
      <AnimatePresence>
        {showMobileFilters && (
          <MobileFiltersModal
            filters={filters}
            onUpdateFilter={updateFilter}
            onClose={() => setShowMobileFilters(false)}
            onClearAll={clearFilters}
            activeFilterCount={activeFilterCount}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

interface CourseCardProps {
  course: Course
  index: number
}

function CourseCard({ course, index }: CourseCardProps) {
  const formatPrice = (price: number, originalPrice?: number) => {
    if (price === 0) return 'Free'

    return (
      <div className="flex items-center space-x-2">
        <span className="font-semibold">${price}</span>
        {originalPrice && originalPrice > price && (
          <span className="text-sm text-gray-500 line-through">${originalPrice}</span>
        )}
      </div>
    )
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="group bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300"
    >
      <Link href={course.isEnrolled ? `/student/courses/${course.slug || course.id}` : `/courses/${course.slug || course.id}`}>
        {/* Course Image */}
        <div className="relative h-48 bg-gradient-to-br from-blue-500 to-indigo-600 overflow-hidden">
          {course.thumbnailImage ? (
            <img
              src={course.thumbnailImage}
              alt={course.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <AcademicCapIcon className="w-16 h-16 text-white/50" />
            </div>
          )}

          {/* Price Badge */}
          <div className="absolute top-4 right-4">
            <div className="px-3 py-1 bg-white/90 text-gray-800 rounded-full text-sm font-semibold">
              {formatPrice(course.price, course.originalPrice)}
            </div>
          </div>

          {/* Enrolled Badge */}
          {course.isEnrolled && (
            <div className="absolute top-4 left-4">
              <span className="px-3 py-1 bg-green-500/90 text-white rounded-full text-xs font-medium">
                Enrolled
              </span>
            </div>
          )}
        </div>

        {/* Course Content */}
        <div className="p-6">
          <div className="flex items-start justify-between mb-3">
            <h3 className="text-lg font-semibold text-gray-800 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
              {course.title}
            </h3>
          </div>

          {course.shortDescription && (
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {course.shortDescription}
            </p>
          )}

          {/* Instructor */}
          <div className="flex items-center space-x-2 mb-4">
            {course.instructor.image ? (
              <img
                src={course.instructor.image}
                alt={course.instructor.name}
                className="w-6 h-6 rounded-full object-cover"
              />
            ) : (
              <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
            )}
            <span className="text-sm text-gray-600">{course.instructor.name}</span>
          </div>

          {/* Course Stats */}
          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <UsersIcon className="w-4 h-4 mr-1" />
                <span>{course.studentsCount || 0}</span>
              </div>
              <div className="flex items-center">
                <ClockIcon className="w-4 h-4 mr-1" />
                <span>{formatDuration(course.totalDuration)}</span>
              </div>
              <div className="flex items-center">
                <AcademicCapIcon className="w-4 h-4 mr-1" />
                <span>{course.totalLessons}</span>
              </div>
            </div>
            {course.rating && (
              <div className="flex items-center">
                <StarIcon className="w-4 h-4 mr-1 text-yellow-400 fill-current" />
                <span>{course.rating.toFixed(1)}</span>
              </div>
            )}
          </div>

          {/* Course Meta */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{course.category}</span>
            <span>{course.level}</span>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}

interface MobileFiltersModalProps {
  filters: SearchFilters
  onUpdateFilter: (key: keyof SearchFilters, value: string) => void
  onClose: () => void
  onClearAll: () => void
  activeFilterCount: number
}

function MobileFiltersModal({
  filters,
  onUpdateFilter,
  onClose,
  onClearAll,
  activeFilterCount
}: MobileFiltersModalProps) {
  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, y: '100%' }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: '100%' }}
        className="absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl max-h-[80vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FunnelIcon className="w-6 h-6 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-800">Filters</h3>
          </div>
          <div className="flex items-center space-x-3">
            {activeFilterCount > 0 && (
              <button
                onClick={onClearAll}
                className="text-sm text-blue-600 hover:text-blue-700 transition-colors duration-200"
              >
                Clear all
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="p-6 space-y-6 overflow-y-auto max-h-96">
          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              value={filters.category}
              onChange={(e) => onUpdateFilter('category', e.target.value)}
              className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Level */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
            <select
              value={filters.level}
              onChange={(e) => onUpdateFilter('level', e.target.value)}
              className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              <option value="">All Levels</option>
              {levels.map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>

          {/* Price Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Price</label>
            <select
              value={filters.priceRange}
              onChange={(e) => onUpdateFilter('priceRange', e.target.value)}
              className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              <option value="">All Prices</option>
              {priceRanges.map(range => (
                <option key={range.value} value={range.value}>{range.label}</option>
              ))}
            </select>
          </div>

          {/* Sort By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
            <select
              value={filters.sortBy}
              onChange={(e) => onUpdateFilter('sortBy', e.target.value)}
              className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Apply Button */}
        <div className="p-6 border-t border-gray-200">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={onClose}
            className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
          >
            Apply Filters
          </motion.button>
        </div>
      </motion.div>
    </div>
  )
}
