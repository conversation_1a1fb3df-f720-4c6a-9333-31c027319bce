import { Server as HTTPServer } from 'http'
import { Server as SocketIOServer, Socket } from 'socket.io'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

interface AuthenticatedSocket extends Socket {
  userId?: string
  userRole?: string
}

interface SocketUser {
  id: string
  name: string
  email: string
  role: string
  socketId: string
  joinedAt: Date
  lastSeen: Date
}

interface QuizSession {
  id: string
  quizId: string
  participants: Map<string, SocketUser>
  startedAt: Date
  status: 'waiting' | 'active' | 'completed'
}

interface NotificationData {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  userId?: string
  data?: any
  createdAt: Date
}

class SocketManager {
  private io: SocketIOServer
  private connectedUsers: Map<string, SocketUser> = new Map()
  private quizSessions: Map<string, QuizSession> = new Map()
  private userSockets: Map<string, string> = new Map() // userId -> socketId

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.setupMiddleware()
    this.setupEventHandlers()
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token
        if (!token) {
          return next(new Error('Authentication token required'))
        }

        // In a real implementation, you'd verify the JWT token
        // For now, we'll use a simple approach
        const userId = socket.handshake.auth.userId
        const userRole = socket.handshake.auth.userRole

        if (!userId) {
          return next(new Error('Invalid authentication'))
        }

        socket.userId = userId
        socket.userRole = userRole
        next()
      } catch (error) {
        next(new Error('Authentication failed'))
      }
    })
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`User ${socket.userId} connected with socket ${socket.id}`)
      
      this.handleUserConnection(socket)
      this.setupQuizEvents(socket)
      this.setupNotificationEvents(socket)
      this.setupChatEvents(socket)
      this.setupCollaborationEvents(socket)
      
      socket.on('disconnect', () => {
        this.handleUserDisconnection(socket)
      })
    })
  }

  private handleUserConnection(socket: AuthenticatedSocket) {
    if (!socket.userId) return

    // Store user connection
    const user: SocketUser = {
      id: socket.userId,
      name: socket.handshake.auth.userName || 'Unknown',
      email: socket.handshake.auth.userEmail || '',
      role: socket.userRole || 'student',
      socketId: socket.id,
      joinedAt: new Date(),
      lastSeen: new Date()
    }

    this.connectedUsers.set(socket.id, user)
    this.userSockets.set(socket.userId, socket.id)

    // Join user-specific room
    socket.join(`user:${socket.userId}`)
    
    // Join role-specific room
    socket.join(`role:${user.role}`)

    // Emit user online status
    this.io.emit('user:online', {
      userId: socket.userId,
      name: user.name,
      role: user.role
    })

    // Send welcome message
    socket.emit('connection:success', {
      message: 'Connected successfully',
      userId: socket.userId,
      connectedUsers: Array.from(this.connectedUsers.values()).length
    })
  }

  private handleUserDisconnection(socket: AuthenticatedSocket) {
    if (!socket.userId) return

    console.log(`User ${socket.userId} disconnected`)

    // Remove from connected users
    this.connectedUsers.delete(socket.id)
    this.userSockets.delete(socket.userId)

    // Remove from quiz sessions
    this.quizSessions.forEach((session, sessionId) => {
      if (session.participants.has(socket.userId!)) {
        session.participants.delete(socket.userId!)
        this.io.to(`quiz:${sessionId}`).emit('quiz:participant_left', {
          userId: socket.userId,
          participantCount: session.participants.size
        })
      }
    })

    // Emit user offline status
    this.io.emit('user:offline', {
      userId: socket.userId
    })
  }

  private setupQuizEvents(socket: AuthenticatedSocket) {
    // Legacy quiz events (keeping for backward compatibility)
    this.setupLegacyQuizEvents(socket)

    // New live quiz events
    this.setupLiveQuizEvents(socket)
  }

  private setupLegacyQuizEvents(socket: AuthenticatedSocket) {
    // Join quiz session
    socket.on('quiz:join', async (data: { quizId: string }) => {
      if (!socket.userId) return

      const { quizId } = data
      const sessionId = `quiz:${quizId}`

      // Create or get quiz session
      if (!this.quizSessions.has(sessionId)) {
        this.quizSessions.set(sessionId, {
          id: sessionId,
          quizId,
          participants: new Map(),
          startedAt: new Date(),
          status: 'waiting'
        })
      }

      const session = this.quizSessions.get(sessionId)!
      const user = this.connectedUsers.get(socket.id)!

      // Add user to session
      session.participants.set(socket.userId, user)
      socket.join(sessionId)

      // Notify other participants
      socket.to(sessionId).emit('quiz:participant_joined', {
        userId: socket.userId,
        name: user.name,
        participantCount: session.participants.size
      })

      // Send session info to user
      socket.emit('quiz:joined', {
        sessionId,
        quizId,
        participantCount: session.participants.size,
        participants: Array.from(session.participants.values())
      })
    })

    // Leave quiz session
    socket.on('quiz:leave', (data: { quizId: string }) => {
      if (!socket.userId) return

      const sessionId = `quiz:${data.quizId}`
      const session = this.quizSessions.get(sessionId)

      if (session && session.participants.has(socket.userId)) {
        session.participants.delete(socket.userId)
        socket.leave(sessionId)

        socket.to(sessionId).emit('quiz:participant_left', {
          userId: socket.userId,
          participantCount: session.participants.size
        })
      }
    })

    // Quiz progress update
    socket.on('quiz:progress', (data: { 
      quizId: string
      questionIndex: number
      timeRemaining: number
      answered: boolean
    }) => {
      if (!socket.userId) return

      const sessionId = `quiz:${data.quizId}`
      
      // Broadcast progress to other participants (for live leaderboard)
      socket.to(sessionId).emit('quiz:participant_progress', {
        userId: socket.userId,
        ...data
      })
    })

    // Quiz completion
    socket.on('quiz:completed', (data: {
      quizId: string
      score: number
      timeSpent: number
      rank?: number
    }) => {
      if (!socket.userId) return

      const sessionId = `quiz:${data.quizId}`
      
      // Broadcast completion to session
      socket.to(sessionId).emit('quiz:participant_completed', {
        userId: socket.userId,
        ...data
      })
    })
  }

  private setupLiveQuizEvents(socket: AuthenticatedSocket) {
    // Join live quiz session
    socket.on('live-quiz:join-session', async (data: { sessionId: string }) => {
      if (!socket.userId) return

      const { sessionId } = data
      const roomId = `live-quiz:${sessionId}`

      socket.join(roomId)

      // Notify others in the session
      socket.to(roomId).emit('live-quiz:participant-joined', {
        sessionId,
        participant: {
          userId: socket.userId,
          userName: this.connectedUsers.get(socket.id)?.name || 'Unknown',
          joinedAt: new Date()
        }
      })
    })

    // Leave live quiz session
    socket.on('live-quiz:leave-session', async (data: { sessionId: string }) => {
      if (!socket.userId) return

      const { sessionId } = data
      const roomId = `live-quiz:${sessionId}`

      socket.leave(roomId)

      // Notify others in the session
      socket.to(roomId).emit('live-quiz:participant-left', {
        sessionId,
        participant: {
          userId: socket.userId,
          userName: this.connectedUsers.get(socket.id)?.name || 'Unknown',
          leftAt: new Date()
        }
      })
    })

    // Submit answer in live quiz
    socket.on('live-quiz:submit-answer', async (data: {
      sessionId: string
      questionId: string
      answer: any
      timeSpent: number
    }) => {
      if (!socket.userId) return

      const roomId = `live-quiz:${data.sessionId}`

      // Broadcast answer submission (without revealing the answer)
      socket.to(roomId).emit('live-quiz:answer-submitted', {
        sessionId: data.sessionId,
        participant: {
          userId: socket.userId,
          userName: this.connectedUsers.get(socket.id)?.name || 'Unknown',
          questionId: data.questionId,
          timeSpent: data.timeSpent,
          submittedAt: new Date()
        }
      })
    })

    // Request current question
    socket.on('live-quiz:request-current-question', async (data: { sessionId: string }) => {
      if (!socket.userId) return

      // This would typically fetch from database and send current question
      // For now, just acknowledge the request
      socket.emit('live-quiz:current-question-requested', {
        sessionId: data.sessionId,
        userId: socket.userId
      })
    })

    // Sync participant progress
    socket.on('live-quiz:sync-progress', async (data: {
      sessionId: string
      currentQuestion: number
      score: number
      timeSpent: number
    }) => {
      if (!socket.userId) return

      const roomId = `live-quiz:${data.sessionId}`

      // Broadcast progress update to session (for live leaderboard)
      socket.to(roomId).emit('live-quiz:participant-progress', {
        sessionId: data.sessionId,
        participant: {
          userId: socket.userId,
          userName: this.connectedUsers.get(socket.id)?.name || 'Unknown',
          currentQuestion: data.currentQuestion,
          score: data.score,
          timeSpent: data.timeSpent,
          updatedAt: new Date()
        }
      })
    })

    // Handle disconnection from live quiz
    socket.on('disconnect', () => {
      if (!socket.userId) return

      // Notify all live quiz sessions this user was part of
      const rooms = Array.from(socket.rooms).filter(room => room.startsWith('live-quiz:'))

      rooms.forEach(roomId => {
        const sessionId = roomId.replace('live-quiz:', '')
        socket.to(roomId).emit('live-quiz:participant-disconnected', {
          sessionId,
          participant: {
            userId: socket.userId,
            userName: this.connectedUsers.get(socket.id)?.name || 'Unknown',
            disconnectedAt: new Date()
          }
        })
      })
    })
  }

  private setupNotificationEvents(socket: AuthenticatedSocket) {
    // Send notification to specific user
    socket.on('notification:send', (data: {
      targetUserId: string
      type: 'info' | 'success' | 'warning' | 'error'
      title: string
      message: string
      data?: any
    }) => {
      const notification: NotificationData = {
        id: Math.random().toString(36).substring(7),
        ...data,
        createdAt: new Date()
      }

      // Send to specific user
      this.io.to(`user:${data.targetUserId}`).emit('notification:received', notification)
    })

    // Broadcast notification to all users
    socket.on('notification:broadcast', (data: {
      type: 'info' | 'success' | 'warning' | 'error'
      title: string
      message: string
      data?: any
    }) => {
      const notification: NotificationData = {
        id: Math.random().toString(36).substring(7),
        ...data,
        createdAt: new Date()
      }

      this.io.emit('notification:received', notification)
    })
  }

  private setupChatEvents(socket: AuthenticatedSocket) {
    console.log(`🔧 Setting up chat events for user ${socket.userId}`)

    // Join chat room
    socket.on('chat:join', (data: { roomId: string }) => {
      console.log(`👥 User ${socket.userId} joining chat room: ${data.roomId}`)
      socket.join(`chat:${data.roomId}`)

      socket.to(`chat:${data.roomId}`).emit('chat:user_joined', {
        userId: socket.userId,
        name: this.connectedUsers.get(socket.id)?.name
      })
    })

    // Send message
    socket.on('chat:message', async (data: {
      roomId: string
      message: string
      type?: 'text' | 'image' | 'file'
    }) => {
      if (!socket.userId) return

      console.log(`💬 Message from ${socket.userId} in room ${data.roomId}: ${data.message}`)
      const user = this.connectedUsers.get(socket.id)

      try {
        // Save message to database
        const savedMessage = await prisma.chatMessage.create({
          data: {
            roomId: data.roomId,
            userId: socket.userId,
            message: data.message,
            type: data.type || 'text'
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
                role: true
              }
            }
          }
        })

        const messageData = {
          id: savedMessage.id,
          userId: savedMessage.userId,
          userName: savedMessage.user.name || 'Unknown',
          message: savedMessage.message,
          type: savedMessage.type,
          timestamp: savedMessage.createdAt,
          roomId: savedMessage.roomId
        }

        // Broadcast to room
        this.io.to(`chat:${data.roomId}`).emit('chat:message_received', messageData)
      } catch (error) {
        console.error('Error saving chat message:', error)
        // Still broadcast the message even if saving fails
        const messageData = {
          id: Math.random().toString(36).substring(7),
          userId: socket.userId,
          userName: user?.name || 'Unknown',
          message: data.message,
          type: data.type || 'text',
          timestamp: new Date(),
          roomId: data.roomId
        }
        this.io.to(`chat:${data.roomId}`).emit('chat:message_received', messageData)
      }
    })

    // Typing indicator
    socket.on('chat:typing', (data: { roomId: string, isTyping: boolean }) => {
      if (!socket.userId) return

      socket.to(`chat:${data.roomId}`).emit('chat:user_typing', {
        userId: socket.userId,
        userName: this.connectedUsers.get(socket.id)?.name,
        isTyping: data.isTyping
      })
    })
  }

  private setupCollaborationEvents(socket: AuthenticatedSocket) {
    // Document collaboration
    socket.on('doc:join', (data: { documentId: string }) => {
      socket.join(`doc:${data.documentId}`)
      
      socket.to(`doc:${data.documentId}`).emit('doc:user_joined', {
        userId: socket.userId,
        name: this.connectedUsers.get(socket.id)?.name
      })
    })

    // Document changes
    socket.on('doc:change', (data: {
      documentId: string
      changes: any
      version: number
    }) => {
      if (!socket.userId) return

      socket.to(`doc:${data.documentId}`).emit('doc:changes_received', {
        userId: socket.userId,
        changes: data.changes,
        version: data.version
      })
    })

    // Cursor position
    socket.on('doc:cursor', (data: {
      documentId: string
      position: { line: number, column: number }
    }) => {
      if (!socket.userId) return

      socket.to(`doc:${data.documentId}`).emit('doc:cursor_moved', {
        userId: socket.userId,
        userName: this.connectedUsers.get(socket.id)?.name,
        position: data.position
      })
    })
  }

  // Public methods for external use
  public sendNotificationToUser(userId: string, notification: Omit<NotificationData, 'id' | 'createdAt'>) {
    const fullNotification: NotificationData = {
      id: Math.random().toString(36).substring(7),
      ...notification,
      createdAt: new Date()
    }

    this.io.to(`user:${userId}`).emit('notification:received', fullNotification)
  }

  public broadcastNotification(notification: Omit<NotificationData, 'id' | 'createdAt'>) {
    const fullNotification: NotificationData = {
      id: Math.random().toString(36).substring(7),
      ...notification,
      createdAt: new Date()
    }

    this.io.emit('notification:received', fullNotification)
  }

  public broadcastToAll(eventName: string, data: any) {
    this.io.emit(eventName, data)
  }

  public broadcastToRoom(roomId: string, eventName: string, data: any) {
    this.io.to(roomId).emit(eventName, data)
  }

  public getConnectedUsers(): SocketUser[] {
    return Array.from(this.connectedUsers.values())
  }

  public getQuizSessions(): QuizSession[] {
    return Array.from(this.quizSessions.values())
  }

  public isUserOnline(userId: string): boolean {
    return this.userSockets.has(userId)
  }
}

let socketManager: SocketManager | null = null

export function initializeSocketServer(server: HTTPServer): SocketManager {
  if (!socketManager) {
    socketManager = new SocketManager(server)
  }
  return socketManager
}

export function getSocketManager(): SocketManager | null {
  return socketManager
}

export type { SocketUser, QuizSession, NotificationData }
