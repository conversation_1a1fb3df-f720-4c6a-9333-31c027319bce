import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  lessonId: z.string().optional()
})

const createQuizSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  instructions: z.string().optional(),
  lessonId: z.string().optional(), // Optional: quiz can be standalone or part of lesson
  timeLimit: z.number().int().min(1).optional(), // Time limit in minutes
  passingScore: z.number().int().min(0).max(100).optional(),
  maxAttempts: z.number().int().min(1).default(1),
  order: z.number().int().min(0).optional(),
  isPublished: z.boolean().default(false),
  questions: z.array(z.object({
    type: z.enum(['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER', 'MATCHING']),
    text: z.string().min(1, 'Question text is required'),
    options: z.array(z.string()).default([]),
    correctAnswer: z.string().min(1, 'Correct answer is required'),
    explanation: z.string().optional(),
    points: z.number().int().min(1).default(1),
    order: z.number().int().min(0)
  })).min(1, 'At least one question is required')
})

// GET /api/admin/courses/[id]/quizzes - Get course quizzes
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateQuery: querySchema
  },
  async (request: NextRequest, { params, validatedQuery }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const { page = 1, limit = 20, lessonId } = validatedQuery

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true, title: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Build where clause
      const where: any = { courseId }
      if (lessonId) {
        where.lessonId = lessonId
      }

      // Get total count for pagination
      const total = await prisma.courseQuiz.count({ where })

      // Get quizzes with pagination
      const quizzes = await prisma.courseQuiz.findMany({
        where,
        orderBy: [
          { order: 'asc' },
          { createdAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit,
        include: {
          lesson: {
            select: {
              id: true,
              title: true,
              chapter: {
                select: {
                  id: true,
                  title: true,
                  section: {
                    select: {
                      id: true,
                      title: true
                    }
                  }
                }
              }
            }
          },
          questions: {
            orderBy: { order: 'asc' },
            select: {
              id: true,
              type: true,
              text: true,
              points: true,
              order: true
            }
          },
          _count: {
            select: { attempts: true }
          }
        }
      })

      // Add statistics to quizzes
      const quizzesWithStats = quizzes.map(quiz => ({
        ...quiz,
        totalQuestions: quiz.questions.length,
        totalPoints: quiz.questions.reduce((acc, q) => acc + q.points, 0),
        totalAttempts: quiz._count.attempts
      }))

      return APIResponse.success({
        course,
        quizzes: quizzesWithStats,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })
    } catch (error) {
      console.error('Error fetching course quizzes:', error)
      return APIResponse.error('Failed to fetch course quizzes', 500)
    }
  }
)

// POST /api/admin/courses/[id]/quizzes - Create new quiz
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createQuizSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // If lessonId is provided, verify lesson exists and belongs to this course
      if (validatedBody.lessonId) {
        const lesson = await prisma.courseLesson.findFirst({
          where: {
            id: validatedBody.lessonId,
            chapter: {
              section: {
                courseId
              }
            }
          }
        })

        if (!lesson) {
          return APIResponse.error('Lesson not found or does not belong to this course', 400)
        }
      }

      // Get the next order number if not provided
      let order = validatedBody.order
      if (order === undefined) {
        const lastQuiz = await prisma.courseQuiz.findFirst({
          where: { courseId },
          orderBy: { order: 'desc' },
          select: { order: true }
        })
        order = (lastQuiz?.order || 0) + 1
      }

      // Create quiz with questions in a transaction
      const quiz = await prisma.$transaction(async (tx) => {
        // Create the quiz
        const newQuiz = await tx.courseQuiz.create({
          data: {
            courseId,
            lessonId: validatedBody.lessonId,
            title: validatedBody.title,
            description: validatedBody.description,
            instructions: validatedBody.instructions,
            timeLimit: validatedBody.timeLimit,
            passingScore: validatedBody.passingScore,
            maxAttempts: validatedBody.maxAttempts,
            order,
            isPublished: validatedBody.isPublished
          }
        })

        // Create questions
        const questions = await Promise.all(
          validatedBody.questions.map(question =>
            tx.courseQuizQuestion.create({
              data: {
                quizId: newQuiz.id,
                type: question.type,
                text: question.text,
                options: question.options,
                correctAnswer: question.correctAnswer,
                explanation: question.explanation,
                points: question.points,
                order: question.order
              }
            })
          )
        )

        return { ...newQuiz, questions }
      })

      return APIResponse.success({
        message: 'Quiz created successfully',
        quiz
      })
    } catch (error) {
      console.error('Error creating course quiz:', error)
      return APIResponse.error('Failed to create course quiz', 500)
    }
  }
)
