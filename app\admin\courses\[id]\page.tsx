'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { 
  ArrowLeftIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  Bars3Icon,
  PlayIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import SectionModal from '@/components/admin/modals/section-modal'
import ChapterModal from '@/components/admin/modals/chapter-modal'
import LessonModal from '@/components/admin/modals/lesson-modal'
import ConfirmationModal from '@/components/admin/modals/confirmation-modal'
import SortableContainer from '@/components/admin/sortable/sortable-container'
import SortableItem from '@/components/admin/sortable/sortable-item'

interface CourseSection {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  chapters: CourseChapter[]
  totalChapters: number
  totalLessons: number
  totalDuration: number
}

interface CourseChapter {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  lessons: CourseLesson[]
}

interface CourseLesson {
  id: string
  title: string
  description?: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  order: number
  duration?: number
  isPublished: boolean
  isFree: boolean
  video?: {
    id: string
    url: string
    duration?: number
  }
}

interface Course {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  category?: string
  level?: string
  thumbnailImage?: string
  isPublished: boolean
  isActive: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
  instructor: {
    id: string
    name: string
    email: string
    image?: string
  }
  sections: CourseSection[]
  totalLessons: number
  totalDuration: number
  totalQuizzes: number
  totalQuestions: number
  _count: {
    enrollments: number
    discussions: number
    certificates: number
  }
}

export default function CourseDetailPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [loading, setLoading] = useState(true)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())

  // Modal states
  const [sectionModalOpen, setSectionModalOpen] = useState(false)
  const [chapterModalOpen, setChapterModalOpen] = useState(false)
  const [lessonModalOpen, setLessonModalOpen] = useState(false)
  const [editingSection, setEditingSection] = useState<any>(null)
  const [editingChapter, setEditingChapter] = useState<any>(null)
  const [editingLesson, setEditingLesson] = useState<any>(null)
  const [selectedSectionId, setSelectedSectionId] = useState<string>('')
  const [selectedChapterId, setSelectedChapterId] = useState<string>('')

  // Confirmation modal states
  const [confirmationModal, setConfirmationModal] = useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    loading: false
  })

  useEffect(() => {
    if (courseId) {
      fetchCourse()
    }
  }, [courseId])

  const fetchCourse = async () => {
    try {
      setLoading(true)
      console.log('Fetching course with ID:', courseId)

      const response = await fetch(`/api/admin/courses/${courseId}`)
      console.log('Response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('API Error:', errorData)
        throw new Error(errorData.message || 'Failed to fetch course')
      }

      const result = await response.json()
      console.log('Raw API response:', result)

      const data = result.data || result // Handle APIResponse format
      const course = data.course || data // Handle nested course object

      console.log('Processed course data:', course)

      if (!course) {
        throw new Error('No course data received from API')
      }

      setCourse(course)

      // Expand all sections by default (with safety check)
      if (course && course.sections && Array.isArray(course.sections)) {
        console.log('Found sections:', course.sections.length)
        const sectionIds = new Set<string>(course.sections.map((s: CourseSection) => s.id))
        setExpandedSections(sectionIds)
      } else {
        console.log('No sections found in course data, sections:', course?.sections)
        setExpandedSections(new Set())
      }
    } catch (error) {
      console.error('Error fetching course:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to load course')
      router.push('/admin/courses')
    } finally {
      setLoading(false)
    }
  }

  // Modal handlers
  const openSectionModal = (section?: any) => {
    setEditingSection(section || null)
    setSectionModalOpen(true)
  }

  const openChapterModal = (sectionId: string, chapter?: any) => {
    setSelectedSectionId(sectionId)
    setEditingChapter(chapter || null)
    setChapterModalOpen(true)
  }

  const openLessonModal = (sectionId: string, chapterId: string, lesson?: any) => {
    setSelectedSectionId(sectionId)
    setSelectedChapterId(chapterId)
    setEditingLesson(lesson || null)
    setLessonModalOpen(true)
  }

  const closeModals = () => {
    setSectionModalOpen(false)
    setChapterModalOpen(false)
    setLessonModalOpen(false)
    setEditingSection(null)
    setEditingChapter(null)
    setEditingLesson(null)
    setSelectedSectionId('')
    setSelectedChapterId('')
  }

  const handleModalSuccess = () => {
    fetchCourse() // Refresh course data
  }

  // Delete handlers
  const handleDeleteSection = (sectionId: string, sectionTitle: string) => {
    setConfirmationModal({
      isOpen: true,
      title: 'Delete Section',
      message: `Are you sure you want to delete the section "${sectionTitle}"? This action cannot be undone and will also delete all chapters and lessons within this section.`,
      onConfirm: () => performDeleteSection(sectionId),
      loading: false
    })
  }

  const performDeleteSection = async (sectionId: string) => {
    setConfirmationModal(prev => ({ ...prev, loading: true }))

    try {
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to delete section')
      }

      const result = await response.json()
      toast.success(result.message || 'Section deleted successfully')
      fetchCourse() // Refresh course data
      setConfirmationModal(prev => ({ ...prev, isOpen: false, loading: false }))
    } catch (error: any) {
      console.error('Error deleting section:', error)
      toast.error(error.message || 'Failed to delete section')
      setConfirmationModal(prev => ({ ...prev, loading: false }))
    }
  }

  const handleDeleteChapter = (sectionId: string, chapterId: string, chapterTitle: string) => {
    setConfirmationModal({
      isOpen: true,
      title: 'Delete Chapter',
      message: `Are you sure you want to delete the chapter "${chapterTitle}"? This action cannot be undone and will also delete all lessons within this chapter.`,
      onConfirm: () => performDeleteChapter(sectionId, chapterId),
      loading: false
    })
  }

  const performDeleteChapter = async (sectionId: string, chapterId: string) => {
    setConfirmationModal(prev => ({ ...prev, loading: true }))

    try {
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to delete chapter')
      }

      const result = await response.json()
      toast.success(result.message || 'Chapter deleted successfully')
      fetchCourse() // Refresh course data
      setConfirmationModal(prev => ({ ...prev, isOpen: false, loading: false }))
    } catch (error: any) {
      console.error('Error deleting chapter:', error)
      toast.error(error.message || 'Failed to delete chapter')
      setConfirmationModal(prev => ({ ...prev, loading: false }))
    }
  }

  const handleDeleteLesson = (sectionId: string, chapterId: string, lessonId: string, lessonTitle: string) => {
    setConfirmationModal({
      isOpen: true,
      title: 'Delete Lesson',
      message: `Are you sure you want to delete the lesson "${lessonTitle}"? This action cannot be undone and will permanently remove all lesson content, videos, and attachments.`,
      onConfirm: () => performDeleteLesson(sectionId, chapterId, lessonId),
      loading: false
    })
  }

  const performDeleteLesson = async (sectionId: string, chapterId: string, lessonId: string) => {
    setConfirmationModal(prev => ({ ...prev, loading: true }))

    try {
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons/${lessonId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to delete lesson')
      }

      const result = await response.json()
      toast.success(result.message || 'Lesson deleted successfully')
      fetchCourse() // Refresh course data
      setConfirmationModal(prev => ({ ...prev, isOpen: false, loading: false }))
    } catch (error: any) {
      console.error('Error deleting lesson:', error)
      toast.error(error.message || 'Failed to delete lesson')
      setConfirmationModal(prev => ({ ...prev, loading: false }))
    }
  }

  // Reorder handlers
  const handleReorderSections = async (reorderedSections: Array<{ id: string; order: number }>) => {
    try {
      const response = await fetch(`/api/admin/courses/${courseId}/sections/reorder`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sections: reorderedSections })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to reorder sections')
      }

      toast.success('Sections reordered successfully')
      fetchCourse() // Refresh course data
    } catch (error: any) {
      console.error('Error reordering sections:', error)
      toast.error(error.message || 'Failed to reorder sections')
    }
  }

  const handleReorderChapters = async (sectionId: string, reorderedChapters: Array<{ id: string; order: number }>) => {
    try {
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/reorder`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ chapters: reorderedChapters })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to reorder chapters')
      }

      toast.success('Chapters reordered successfully')
      fetchCourse() // Refresh course data
    } catch (error: any) {
      console.error('Error reordering chapters:', error)
      toast.error(error.message || 'Failed to reorder chapters')
    }
  }

  const handleReorderLessons = async (sectionId: string, chapterId: string, reorderedLessons: Array<{ id: string; order: number }>) => {
    try {
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons/reorder`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ lessons: reorderedLessons })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to reorder lessons')
      }

      toast.success('Lessons reordered successfully')
      fetchCourse() // Refresh course data
    } catch (error: any) {
      console.error('Error reordering lessons:', error)
      toast.error(error.message || 'Failed to reorder lessons')
    }
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const togglePublishStatus = async () => {
    if (!course) return

    try {
      const response = await fetch(`/api/admin/courses/${courseId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isPublished: !course.isPublished })
      })

      if (!response.ok) throw new Error('Failed to update course')

      const data = await response.json()
      toast.success(data.message)
      fetchCourse()
    } catch (error) {
      console.error('Error updating course:', error)
      toast.error('Failed to update course')
    }
  }



  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0m'
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading course...</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-300">Course not found</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Course not found</h2>
          <p className="text-gray-600 mb-6">The course you're looking for doesn't exist.</p>
          <Link href="/admin/courses">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Back to Courses
            </motion.button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border-b border-white/20 dark:border-slate-700/20 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <Link href="/admin/courses">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 rounded-lg transition-colors duration-200"
                >
                  <ArrowLeftIcon className="w-5 h-5" />
                </motion.button>
              </Link>
              <div className="min-w-0 flex-1">
                <h1 className="text-xl lg:text-2xl font-bold text-gray-800 dark:text-white truncate">{course.title}</h1>
                <div className="flex flex-wrap items-center gap-2 lg:gap-4 text-sm text-gray-600 dark:text-gray-300 mt-1">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    course.isPublished
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                      : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
                  }`}>
                    {course.isPublished ? 'Published' : 'Draft'}
                  </span>
                  <span className="hidden sm:inline">{course.totalLessons} lessons</span>
                  <span className="hidden sm:inline">{formatDuration(course.totalDuration * 60)}</span>
                  <span className="hidden sm:inline">{course._count.enrollments} students</span>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-2 lg:gap-3">
              <Link href={`/admin/courses/${courseId}/edit`}>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-3 lg:px-4 py-2 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200 text-sm lg:text-base"
                >
                  <PencilIcon className="w-4 h-4 mr-1 lg:mr-2 inline" />
                  <span className="hidden sm:inline">Edit</span>
                </motion.button>
              </Link>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={togglePublishStatus}
                className={`px-3 lg:px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm lg:text-base ${
                  course.isPublished
                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 hover:bg-yellow-200 dark:hover:bg-yellow-900/50'
                    : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50'
                }`}
              >
                <span className="hidden sm:inline">{course.isPublished ? 'Unpublish' : 'Publish'}</span>
                <span className="sm:hidden">{course.isPublished ? 'Hide' : 'Show'}</span>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => openSectionModal()}
                className="px-3 lg:px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 text-sm lg:text-base"
              >
                <PlusIcon className="w-4 h-4 mr-1 lg:mr-2 inline" />
                <span className="hidden sm:inline">Add Section</span>
                <span className="sm:hidden">Add</span>
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        {/* Course Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8">
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/20 p-4 lg:p-6">
            <div className="text-xl lg:text-2xl font-bold text-blue-600 dark:text-blue-400">{course.totalLessons}</div>
            <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-300">Total Lessons</div>
          </div>
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/20 p-4 lg:p-6">
            <div className="text-xl lg:text-2xl font-bold text-green-600 dark:text-green-400">{course._count.enrollments}</div>
            <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-300">Students</div>
          </div>
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/20 p-4 lg:p-6">
            <div className="text-xl lg:text-2xl font-bold text-purple-600 dark:text-purple-400">{formatDuration(course.totalDuration * 60)}</div>
            <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-300">Duration</div>
          </div>
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/20 p-4 lg:p-6">
            <div className="text-xl lg:text-2xl font-bold text-orange-600 dark:text-orange-400">{course.totalQuizzes}</div>
            <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-300">Quizzes</div>
          </div>
        </div>

        {/* Course Content */}
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/20 p-4 lg:p-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 lg:mb-6 space-y-2 sm:space-y-0">
            <h2 className="text-lg lg:text-xl font-semibold text-gray-800 dark:text-white">Course Content</h2>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {course.sections?.length || 0} sections • {course.totalLessons || 0} lessons
            </div>
          </div>

          {!course.sections || course.sections.length === 0 ? (
            <div className="text-center py-8 lg:py-12">
              <AcademicCapIcon className="w-12 lg:w-16 h-12 lg:h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              <h3 className="text-base lg:text-lg font-semibold text-gray-600 dark:text-gray-300 mb-2">No content yet</h3>
              <p className="text-sm lg:text-base text-gray-500 dark:text-gray-400 mb-6">Start building your course by adding sections</p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => openSectionModal()}
                className="px-4 lg:px-6 py-2 lg:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 text-sm lg:text-base"
              >
                <PlusIcon className="w-4 lg:w-5 h-4 lg:h-5 mr-2 inline" />
                Add First Section
              </motion.button>
            </div>
          ) : (
            <SortableContainer
              items={course.sections.map(section => ({ id: section.id, order: section.order }))}
              onReorder={handleReorderSections}
            >
              <div className="space-y-4">
                {(course.sections || []).map((section) => (
                  <SortableItem key={section.id} id={section.id}>
                    <SectionCard
                      section={section}
                      courseId={courseId}
                      isExpanded={expandedSections.has(section.id)}
                      onToggle={() => toggleSection(section.id)}
                      onEditSection={openSectionModal}
                      onDeleteSection={handleDeleteSection}
                      onAddChapter={(sectionId) => openChapterModal(sectionId)}
                      onEditChapter={(sectionId, chapter) => openChapterModal(sectionId, chapter)}
                      onDeleteChapter={handleDeleteChapter}
                      onAddLesson={(sectionId, chapterId) => openLessonModal(sectionId, chapterId)}
                      onEditLesson={(sectionId, chapterId, lesson) => openLessonModal(sectionId, chapterId, lesson)}
                      onDeleteLesson={handleDeleteLesson}
                      onReorderChapters={handleReorderChapters}
                      onReorderLessons={handleReorderLessons}
                    />
                  </SortableItem>
                ))}
              </div>
            </SortableContainer>
          )}
        </div>
      </div>

      {/* Modals */}
      <SectionModal
        isOpen={sectionModalOpen}
        onClose={closeModals}
        courseId={courseId}
        onSuccess={handleModalSuccess}
        editingSection={editingSection}
      />

      <ChapterModal
        isOpen={chapterModalOpen}
        onClose={closeModals}
        courseId={courseId}
        sectionId={selectedSectionId}
        onSuccess={handleModalSuccess}
        editingChapter={editingChapter}
      />

      <LessonModal
        isOpen={lessonModalOpen}
        onClose={closeModals}
        courseId={courseId}
        sectionId={selectedSectionId}
        chapterId={selectedChapterId}
        onSuccess={handleModalSuccess}
        editingLesson={editingLesson}
      />

      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        onClose={() => setConfirmationModal(prev => ({ ...prev, isOpen: false }))}
        onConfirm={confirmationModal.onConfirm}
        title={confirmationModal.title}
        message={confirmationModal.message}
        loading={confirmationModal.loading}
        confirmText="Delete"
        type="danger"
      />
    </div>
  )
}

interface SectionCardProps {
  section: CourseSection
  courseId: string
  isExpanded: boolean
  onToggle: () => void
  onEditSection: (section: any) => void
  onDeleteSection: (sectionId: string, sectionTitle: string) => void
  onAddChapter: (sectionId: string) => void
  onEditChapter: (sectionId: string, chapter: any) => void
  onDeleteChapter: (sectionId: string, chapterId: string, chapterTitle: string) => void
  onAddLesson: (sectionId: string, chapterId: string) => void
  onEditLesson: (sectionId: string, chapterId: string, lesson: any) => void
  onDeleteLesson: (sectionId: string, chapterId: string, lessonId: string, lessonTitle: string) => void
  onReorderChapters: (sectionId: string, reorderedChapters: Array<{ id: string; order: number }>) => void
  onReorderLessons: (sectionId: string, chapterId: string, reorderedLessons: Array<{ id: string; order: number }>) => void
}

function SectionCard({
  section,
  courseId,
  isExpanded,
  onToggle,
  onEditSection,
  onDeleteSection,
  onAddChapter,
  onEditChapter,
  onDeleteChapter,
  onAddLesson,
  onEditLesson,
  onDeleteLesson,
  onReorderChapters,
  onReorderLessons
}: SectionCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="border border-gray-200 dark:border-slate-700 rounded-xl overflow-hidden"
    >
      {/* Section Header */}
      <div className="bg-gray-50/50 dark:bg-slate-700/50 p-3 lg:p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 lg:space-x-3 min-w-0 flex-1">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onToggle}
              className="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200 flex-shrink-0"
            >
              {isExpanded ? (
                <ChevronDownIcon className="w-4 lg:w-5 h-4 lg:h-5" />
              ) : (
                <ChevronRightIcon className="w-4 lg:w-5 h-4 lg:h-5" />
              )}
            </motion.button>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs font-medium rounded">
                  Section
                </span>
                <h3 className="font-semibold text-gray-800 dark:text-white text-sm lg:text-base truncate">{section.title}</h3>
              </div>
              <div className="flex flex-wrap items-center gap-2 lg:gap-4 text-xs lg:text-sm text-gray-600 dark:text-gray-300">
                <span>{section.} lessons</span>
                <span className="hidden sm:inline">{Math.round(section.totalDuration / 60)}m</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  section.isPublished
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                    : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
                }`}>
                  {section.isPublished ? 'Published' : 'Draft'}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-1 lg:space-x-2 flex-shrink-0">
            <Bars3Icon className="w-3 lg:w-4 h-3 lg:h-4 text-gray-400 dark:text-gray-500 cursor-move hidden sm:block" />
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onEditSection(section)}
              className="p-1.5 lg:p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200"
              title="Edit Section"
            >
              <PencilIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onDeleteSection(section.id, section.title)}
              className="p-1.5 lg:p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors duration-200"
              title="Delete Section"
            >
              <TrashIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Section Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4 space-y-3">
              {section.chapters.length === 0 ? (
                <div className="text-center py-6 lg:py-8">
                  <DocumentTextIcon className="w-10 lg:w-12 h-10 lg:h-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
                  <p className="text-sm lg:text-base text-gray-500 dark:text-gray-400 mb-4">No chapters in this section</p>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => onAddChapter(section.id)}
                    className="px-3 lg:px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200 text-sm lg:text-base"
                  >
                    <PlusIcon className="w-3 lg:w-4 h-3 lg:h-4 mr-2 inline" />
                    Add Chapter
                  </motion.button>
                </div>
              ) : (
                <SortableContainer
                  items={section.chapters.map(chapter => ({ id: chapter.id, order: chapter.order }))}
                  onReorder={(reorderedChapters) => onReorderChapters(section.id, reorderedChapters)}
                >
                  {section.chapters.map((chapter) => (
                    <SortableItem key={chapter.id} id={chapter.id}>
                      <ChapterCard
                        chapter={chapter}
                        courseId={courseId}
                        sectionId={section.id}
                        onEditChapter={onEditChapter}
                        onDeleteChapter={onDeleteChapter}
                        onAddLesson={onAddLesson}
                        onEditLesson={onEditLesson}
                        onDeleteLesson={onDeleteLesson}
                        onReorderLessons={onReorderLessons}
                      />
                    </SortableItem>
                  ))}
                </SortableContainer>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

interface ChapterCardProps {
  chapter: CourseChapter
  courseId: string
  sectionId: string
  onEditChapter: (sectionId: string, chapter: any) => void
  onDeleteChapter: (sectionId: string, chapterId: string, chapterTitle: string) => void
  onAddLesson: (sectionId: string, chapterId: string) => void
  onEditLesson: (sectionId: string, chapterId: string, lesson: any) => void
  onDeleteLesson: (sectionId: string, chapterId: string, lessonId: string, lessonTitle: string) => void
  onReorderLessons: (sectionId: string, chapterId: string, reorderedLessons: Array<{ id: string; order: number }>) => void
}

function ChapterCard({
  chapter,
  courseId,
  sectionId,
  onEditChapter,
  onDeleteChapter,
  onAddLesson,
  onEditLesson,
  onDeleteLesson,
  onReorderLessons
}: ChapterCardProps) {
  return (
    <div className="bg-white/50 dark:bg-slate-700/50 border border-gray-200 dark:border-slate-600 rounded-lg p-3 lg:p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2 lg:space-x-3 min-w-0 flex-1">
          <Bars3Icon className="w-3 lg:w-4 h-3 lg:h-4 text-gray-400 dark:text-gray-500 cursor-move hidden sm:block flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-xs font-medium rounded">
                Chapter
              </span>
              <h4 className="font-medium text-gray-800 dark:text-white text-sm lg:text-base truncate">{chapter.title}</h4>
            </div>
            <div className="flex flex-wrap items-center gap-2 lg:gap-3 text-xs lg:text-sm text-gray-600 dark:text-gray-300">
              <span>{chapter.lessons.length} lessons</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                chapter.isPublished
                  ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                  : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
              }`}>
                {chapter.isPublished ? 'Published' : 'Draft'}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-1 lg:space-x-2 flex-shrink-0">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onAddLesson(sectionId, chapter.id)}
            className="p-1 lg:p-1.5 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors duration-200"
            title="Add Lesson"
          >
            <PlusIcon className="w-3 lg:w-4 h-3 lg:h-4" />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onEditChapter(sectionId, chapter)}
            className="p-1 lg:p-1.5 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-600 rounded transition-colors duration-200"
            title="Edit Chapter"
          >
            <PencilIcon className="w-3 lg:w-4 h-3 lg:h-4" />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onDeleteChapter(sectionId, chapter.id, chapter.title)}
            className="p-1 lg:p-1.5 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors duration-200"
            title="Delete Chapter"
          >
            <TrashIcon className="w-3 lg:w-4 h-3 lg:h-4" />
          </motion.button>
        </div>
      </div>

      {/* Lessons */}
      {chapter.lessons.length > 0 && (
        <div className="ml-7">
          <SortableContainer
            items={chapter.lessons.map(lesson => ({ id: lesson.id, order: lesson.order }))}
            onReorder={(reorderedLessons) => onReorderLessons(sectionId, chapter.id, reorderedLessons)}
          >
            <div className="space-y-2">
              {chapter.lessons.map((lesson) => (
                <SortableItem key={lesson.id} id={lesson.id}>
                  <LessonCard
                    lesson={lesson}
                    courseId={courseId}
                    sectionId={sectionId}
                    chapterId={chapter.id}
                    onEditLesson={onEditLesson}
                    onDeleteLesson={onDeleteLesson}
                  />
                </SortableItem>
              ))}
            </div>
          </SortableContainer>
        </div>
      )}
    </div>
  )
}

interface LessonCardProps {
  lesson: CourseLesson
  courseId: string
  sectionId: string
  chapterId: string
  onEditLesson: (sectionId: string, chapterId: string, lesson: any) => void
  onDeleteLesson: (sectionId: string, chapterId: string, lessonId: string, lessonTitle: string) => void
}

function LessonCard({ lesson, courseId, sectionId, chapterId, onEditLesson, onDeleteLesson }: LessonCardProps) {
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0m'
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  }

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  return (
    <div className="flex items-center justify-between p-2 lg:p-3 bg-white/70 dark:bg-slate-600/70 border border-gray-100 dark:border-slate-600 rounded-lg hover:shadow-sm dark:hover:bg-slate-600/90 transition-all duration-200">
      <div className="flex items-center space-x-2 lg:space-x-3 min-w-0 flex-1">
        <Bars3Icon className="w-3 h-3 text-gray-400 dark:text-gray-500 cursor-move hidden sm:block flex-shrink-0" />
        <div className="text-gray-600 dark:text-gray-300 flex-shrink-0">
          {getLessonIcon(lesson.type)}
        </div>
        <div className="min-w-0 flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="px-2 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs font-medium rounded">
              Lesson
            </span>
            <span className="text-xs lg:text-sm font-medium text-gray-800 dark:text-white truncate">{lesson.title}</span>
          </div>
          <div className="flex flex-wrap items-center gap-1 lg:gap-2">
            <span className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">
              {lesson.type}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {formatDuration(lesson.duration)}
            </span>
            {lesson.isFree && (
              <span className="px-1.5 lg:px-2 py-0.5 lg:py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full flex-shrink-0">
                Free
              </span>
            )}
            <span className={`px-1.5 lg:px-2 py-0.5 lg:py-1 rounded-full text-xs font-medium flex-shrink-0 ${
              lesson.isPublished
                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
            }`}>
              {lesson.isPublished ? 'Published' : 'Draft'}
            </span>
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-1 flex-shrink-0">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => onEditLesson(sectionId, chapterId, lesson)}
          className="p-1 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-500 rounded transition-colors duration-200"
          title="Edit Lesson"
        >
          <PencilIcon className="w-3 h-3" />
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => onDeleteLesson(sectionId, chapterId, lesson.id, lesson.title)}
          className="p-1 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors duration-200"
          title="Delete Lesson"
        >
          <TrashIcon className="w-3 h-3" />
        </motion.button>
      </div>
    </div>
  )
}
