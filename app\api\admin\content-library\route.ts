import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { getBunnyStorage } from '@/lib/bunny-storage'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const uploadFileSchema = z.object({
  folder: z.string().optional(),
  tags: z.array(z.string()).optional(),
  description: z.string().optional()
})

// GET /api/admin/content-library - Get all content library files
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url)
      const search = searchParams.get('search')
      const type = searchParams.get('type') // image, video, document, audio
      const folder = searchParams.get('folder')
      const tags = searchParams.get('tags')?.split(',').filter(Boolean)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')
      const sortBy = searchParams.get('sortBy') || 'createdAt'
      const sortOrder = searchParams.get('sortOrder') || 'desc'

      // Build where clause
      const where: any = {}

      if (search) {
        where.OR = [
          { filename: { contains: search, mode: 'insensitive' } },
          { originalName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (type) {
        where.type = type
      }

      if (folder) {
        where.folder = folder
      }

      if (tags && tags.length > 0) {
        where.tags = {
          hasSome: tags
        }
      }

      // Get total count
      const total = await prisma.contentLibraryFile.count({ where })

      // Get files with pagination
      const files = await prisma.contentLibraryFile.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          _count: {
            select: {
              usages: true
            }
          }
        }
      })

      // Get folder structure
      const folders = await prisma.contentLibraryFile.groupBy({
        by: ['folder'],
        where: { folder: { not: null } },
        _count: {
          folder: true
        }
      })

      // Get file type statistics
      const typeStats = await prisma.contentLibraryFile.groupBy({
        by: ['type'],
        _count: {
          type: true
        },
        _sum: {
          size: true
        }
      })

      // Get all unique tags
      const allFiles = await prisma.contentLibraryFile.findMany({
        select: { tags: true },
        where: { tags: { not: { equals: [] } } }
      })
      
      const allTags = [...new Set(allFiles.flatMap(file => file.tags))]

      return APIResponse.success({
        files: files.map(file => ({
          ...file,
          usageCount: file._count.usages,
          url: file.url,
          thumbnailUrl: file.thumbnailUrl
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        },
        folders: folders.map(f => ({
          name: f.folder,
          fileCount: f._count.folder
        })),
        statistics: {
          totalFiles: total,
          typeBreakdown: typeStats.reduce((acc, stat) => {
            acc[stat.type] = {
              count: stat._count.type,
              totalSize: stat._sum.size || 0
            }
            return acc
          }, {} as Record<string, { count: number; totalSize: number }>),
          totalSize: typeStats.reduce((sum, stat) => sum + (stat._sum.size || 0), 0)
        },
        availableTags: allTags
      })
    } catch (error) {
      console.error('Error fetching content library:', error)
      return APIResponse.error('Failed to fetch content library', 500)
    }
  }
)

// POST /api/admin/content-library - Upload new file
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File
      const folder = formData.get('folder') as string || 'general'
      const description = formData.get('description') as string || ''
      const tagsString = formData.get('tags') as string || ''
      const tags = tagsString ? tagsString.split(',').map(t => t.trim()).filter(Boolean) : []

      if (!file) {
        return APIResponse.error('No file provided', 400)
      }

      // Validate file size (max 100MB)
      const maxSize = 100 * 1024 * 1024
      if (file.size > maxSize) {
        return APIResponse.error('File size exceeds 100MB limit', 400)
      }

      // Determine file type
      const fileType = getFileType(file.type, file.name)

      // Upload to Bunny CDN
      const bunnyStorage = getBunnyStorage()
      const uploadResult = await bunnyStorage.uploadFile(file, {
        folder: `content-library/${folder}`,
        filename: `${Date.now()}_${file.name}`,
        contentType: file.type,
        optimize: fileType === 'image'
      })

      if (!uploadResult.success) {
        return APIResponse.error(`Failed to upload file: ${uploadResult.error}`, 500)
      }

      // Generate thumbnail for images and videos
      let thumbnailUrl = null
      if (fileType === 'image') {
        thumbnailUrl = `${uploadResult.url}?width=300&height=200&fit=crop`
      } else if (fileType === 'video') {
        thumbnailUrl = `${uploadResult.url}?thumbnail=1`
      }

      // Save to database
      const contentFile = await prisma.contentLibraryFile.create({
        data: {
          filename: uploadResult.filename!,
          originalName: file.name,
          url: uploadResult.url!,
          thumbnailUrl,
          type: fileType,
          mimeType: file.type,
          size: uploadResult.size!,
          folder,
          description,
          tags,
          uploadedBy: user.id
        }
      })

      return APIResponse.success({
        message: 'File uploaded successfully',
        file: {
          ...contentFile,
          usageCount: 0
        }
      })
    } catch (error) {
      console.error('Error uploading file:', error)
      return APIResponse.error('Failed to upload file', 500)
    }
  }
)

// Helper function to determine file type
function getFileType(mimeType: string, filename: string): string {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  if (mimeType.startsWith('audio/')) return 'audio'
  
  // Check by extension for documents
  const ext = filename.toLowerCase().split('.').pop()
  const documentExts = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf']
  if (ext && documentExts.includes(ext)) return 'document'
  
  return 'other'
}
