import { NextRequest } from 'next/server'

// Simple test route to check if basic API functionality works
export async function GET(request: NextRequest) {
  try {
    // Test 1: Return static data
    const staticCourses = [
      {
        id: '1',
        productId: 'static-1',
        title: 'Static Test Course 1',
        description: 'This is a static test course',
        price: 100,
        slug: 'static-test-course-1',
        category: 'test',
        features: ['Test Feature 1', 'Test Feature 2']
      },
      {
        id: '2',
        productId: 'static-2',
        title: 'Static Test Course 2',
        description: 'This is another static test course',
        price: 200,
        slug: 'static-test-course-2',
        category: 'test',
        features: ['Test Feature 3', 'Test Feature 4']
      }
    ]

    return Response.json({
      success: true,
      courses: staticCourses,
      total: staticCourses.length,
      message: 'Static test data returned successfully'
    })

  } catch (error: any) {
    return Response.json({
      success: false,
      error: error.message,
      courses: [],
      total: 0
    }, { status: 500 })
  }
}
