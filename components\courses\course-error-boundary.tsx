'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Alert<PERSON><PERSON>gle, RefreshCw, Home, BookOpen } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface CourseErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

interface CourseErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class CourseErrorBoundary extends React.Component<
  CourseErrorBoundaryProps,
  CourseErrorBoundaryState
> {
  constructor(props: CourseErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): CourseErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Course Error Boundary caught an error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <CourseErrorFallback
            error={this.state.error}
            onRetry={() => this.setState({ hasError: false, error: undefined })}
          />
        )
      )
    }

    return this.props.children
  }
}

interface CourseErrorFallbackProps {
  error?: Error
  onRetry?: () => void
  title?: string
  description?: string
  showRetry?: boolean
  showNavigation?: boolean
}

export function CourseErrorFallback({
  error,
  onRetry,
  title = 'Something went wrong',
  description,
  showRetry = true,
  showNavigation = true
}: CourseErrorFallbackProps) {
  const errorMessage = error?.message || description || 'An unexpected error occurred while loading courses.'

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="flex items-center justify-center min-h-[400px] p-4"
    >
      <Card className="w-full max-w-md bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>

          <div className="flex flex-col gap-3">
            {showRetry && onRetry && (
              <Button
                onClick={onRetry}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}

            {showNavigation && (
              <>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/student/courses'}
                  className="w-full"
                >
                  <BookOpen className="h-4 w-4 mr-2" />
                  Browse Courses
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/student'}
                  className="w-full"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go to Dashboard
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Hook for handling course-related errors
export function useCourseErrorHandler() {
  const handleError = (error: unknown, context?: string) => {
    console.error(`Course Error${context ? ` (${context})` : ''}:`, error)
    
    let message = 'An unexpected error occurred'
    
    if (error instanceof Error) {
      message = error.message
    } else if (typeof error === 'string') {
      message = error
    }

    // You can add toast notifications here if needed
    return message
  }

  const handleApiError = async (response: Response, context?: string) => {
    try {
      const errorData = await response.json()
      const message = errorData.message || `API Error: ${response.status} ${response.statusText}`
      handleError(new Error(message), context)
      return message
    } catch {
      const message = `Network Error: ${response.status} ${response.statusText}`
      handleError(new Error(message), context)
      return message
    }
  }

  return { handleError, handleApiError }
}

// Loading state components
export function CourseCardSkeleton() {
  return (
    <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-xl border shadow-lg overflow-hidden animate-pulse">
      <div className="h-48 bg-gray-200 dark:bg-gray-700" />
      <div className="p-6 space-y-4">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full" />
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3" />
        <div className="flex gap-2">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16" />
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16" />
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16" />
        </div>
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20" />
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-24" />
        </div>
      </div>
    </div>
  )
}

export function CourseGridSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <CourseCardSkeleton key={index} />
      ))}
    </div>
  )
}

export function CourseListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="flex items-center gap-4 p-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-lg border shadow-lg animate-pulse"
        >
          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex-shrink-0" />
          <div className="flex-1 space-y-2">
            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full" />
          </div>
          <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded flex-shrink-0" />
        </div>
      ))}
    </div>
  )
}

// Network status indicator
export function NetworkStatusIndicator({ isOnline }: { isOnline: boolean }) {
  if (isOnline) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"
    >
      <Alert variant="destructive" className="bg-red-500 text-white border-red-600">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You're offline. Some features may not work properly.
        </AlertDescription>
      </Alert>
    </motion.div>
  )
}
