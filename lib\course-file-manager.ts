/**
 * Course File Manager
 * 
 * Utility for managing course-specific file organization in Bunny CDN
 */

import { getBunnyStorageWithTest } from './bunny-storage'

export interface CourseFileStructure {
  courseId: string
  images: string[]
  files: string[]
  videos: string[]
}

export interface FileUploadOptions {
  courseId: string
  lessonId?: string
  type: 'image' | 'file' | 'video'
  category?: string // e.g., 'thumbnail', 'attachment', 'lesson-video'
}

export class CourseFileManager {
  private storage: any

  constructor() {
    this.storage = null
  }

  private async getStorage() {
    if (!this.storage) {
      this.storage = await getBunnyStorageWithTest()
    }
    return this.storage
  }

  /**
   * Get the appropriate folder path for a course file
   */
  getCourseFolderPath(courseId: string, fileType: 'images' | 'files' | 'videos'): string {
    return `courses/${courseId}/${fileType}`
  }

  /**
   * Generate a unique filename with course context
   */
  generateCourseFilename(
    originalName: string,
    options: FileUploadOptions
  ): string {
    const timestamp = Date.now()
    const extension = originalName.split('.').pop() || 'bin'
    const cleanName = originalName
      .replace(/\.[^/.]+$/, '') // Remove extension
      .replace(/[^a-zA-Z0-9]/g, '-') // Replace special chars with dash
      .toLowerCase()
      .substring(0, 30) // Limit length

    let prefix = options.type
    if (options.category) {
      prefix = `${options.category}-${options.type}`
    }
    if (options.lessonId) {
      prefix = `${prefix}-${options.lessonId}`
    }

    return `${prefix}-${cleanName}-${timestamp}.${extension}`
  }

  /**
   * Upload a file to the appropriate course folder
   */
  async uploadCourseFile(
    file: File | Buffer,
    options: FileUploadOptions
  ): Promise<{
    success: boolean
    url?: string
    filename?: string
    folder?: string
    size?: number
    error?: string
  }> {
    try {
      const storage = await this.getStorage()
      
      const folderMap = {
        image: 'images',
        file: 'files',
        video: 'videos'
      } as const

      const folderType = folderMap[options.type]
      const folder = this.getCourseFolderPath(options.courseId, folderType)
      
      const filename = this.generateCourseFilename(
        file instanceof File ? file.name : 'file',
        options
      )

      console.log(`Uploading ${options.type} to course folder: ${folder}/${filename}`)

      const uploadResult = await storage.uploadFile(file, {
        folder,
        filename,
        maxSize: this.getMaxFileSize(options.type)
      })

      if (uploadResult.success) {
        return {
          success: true,
          url: uploadResult.url,
          filename: uploadResult.filename,
          folder,
          size: uploadResult.size
        }
      } else {
        return {
          success: false,
          error: uploadResult.error
        }
      }
    } catch (error) {
      console.error('Course file upload error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Get maximum file size based on file type
   */
  private getMaxFileSize(type: 'image' | 'file' | 'video'): number {
    const sizes = {
      image: 5 * 1024 * 1024,   // 5MB
      file: 50 * 1024 * 1024,   // 50MB
      video: 100 * 1024 * 1024  // 100MB
    }
    return sizes[type]
  }

  /**
   * List all files for a specific course
   */
  async listCourseFiles(courseId: string): Promise<CourseFileStructure> {
    try {
      const storage = await this.getStorage()
      
      // Note: This would require implementing a list files method in BunnyStorage
      // For now, return empty structure
      return {
        courseId,
        images: [],
        files: [],
        videos: []
      }
    } catch (error) {
      console.error('Error listing course files:', error)
      return {
        courseId,
        images: [],
        files: [],
        videos: []
      }
    }
  }

  /**
   * Delete a course file
   */
  async deleteCourseFile(fileUrl: string): Promise<boolean> {
    try {
      const storage = await this.getStorage()
      
      // Extract the file path from the URL
      const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL
      if (!pullZoneUrl || !fileUrl.startsWith(pullZoneUrl)) {
        console.error('Invalid file URL for deletion:', fileUrl)
        return false
      }

      const filepath = fileUrl.replace(pullZoneUrl + '/', '')
      console.log(`Deleting course file: ${filepath}`)
      
      return await storage.deleteFile(filepath)
    } catch (error) {
      console.error('Error deleting course file:', error)
      return false
    }
  }

  /**
   * Move files from old structure to new course-specific structure
   */
  async migrateToCourseStructure(
    oldFileUrl: string,
    courseId: string,
    fileType: 'images' | 'files' | 'videos'
  ): Promise<{
    success: boolean
    newUrl?: string
    error?: string
  }> {
    try {
      // This would involve:
      // 1. Download the file from old location
      // 2. Upload to new course-specific location
      // 3. Delete from old location
      // For now, return a placeholder
      
      console.log(`Migration needed: ${oldFileUrl} -> courses/${courseId}/${fileType}/`)
      
      return {
        success: false,
        error: 'Migration not implemented yet'
      }
    } catch (error) {
      console.error('Error migrating file:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Migration failed'
      }
    }
  }

  /**
   * Get course storage statistics
   */
  async getCourseStorageStats(courseId: string): Promise<{
    totalFiles: number
    totalSize: number
    imageCount: number
    fileCount: number
    videoCount: number
  }> {
    try {
      // This would require implementing storage statistics
      // For now, return placeholder data
      return {
        totalFiles: 0,
        totalSize: 0,
        imageCount: 0,
        fileCount: 0,
        videoCount: 0
      }
    } catch (error) {
      console.error('Error getting course storage stats:', error)
      return {
        totalFiles: 0,
        totalSize: 0,
        imageCount: 0,
        fileCount: 0,
        videoCount: 0
      }
    }
  }
}

// Export singleton instance
export const courseFileManager = new CourseFileManager()

// Export utility functions
export function getCourseImageUrl(courseId: string, filename: string): string {
  const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''
  return `${pullZoneUrl}/courses/${courseId}/images/${filename}`
}

export function getCourseFileUrl(courseId: string, filename: string): string {
  const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''
  return `${pullZoneUrl}/courses/${courseId}/files/${filename}`
}

export function getCourseVideoUrl(courseId: string, filename: string): string {
  const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''
  return `${pullZoneUrl}/courses/${courseId}/videos/${filename}`
}

export function extractCourseIdFromUrl(fileUrl: string): string | null {
  const match = fileUrl.match(/\/courses\/([^\/]+)\//)
  return match ? match[1] : null
}

export function getFileTypeFromUrl(fileUrl: string): 'images' | 'files' | 'videos' | null {
  if (fileUrl.includes('/images/')) return 'images'
  if (fileUrl.includes('/files/')) return 'files'
  if (fileUrl.includes('/videos/')) return 'videos'
  return null
}
