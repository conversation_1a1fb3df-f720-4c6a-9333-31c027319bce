import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-response'
import { withAuth } from '@/lib/auth-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const reorderSchema = z.object({
  lessons: z.array(z.object({
    id: z.string(),
    order: z.number()
  }))
})

export const PUT = withAuth(
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string

      if (!courseId || !sectionId || !chapterId) {
        return APIResponse.error('Course ID, Section ID, and Chapter ID are required', 400)
      }

      // Verify chapter exists
      const chapter = await prisma.courseChapter.findUnique({
        where: { 
          id: chapterId,
          sectionId: sectionId,
          section: {
            courseId: courseId
          }
        },
        select: { id: true }
      })

      if (!chapter) {
        return APIResponse.error('Chapter not found', 404)
      }

      // Update lesson orders
      const updatePromises = validatedBody.lessons.map((lesson: any) =>
        prisma.courseLesson.update({
          where: { id: lesson.id },
          data: { order: lesson.order }
        })
      )

      await Promise.all(updatePromises)

      return APIResponse.success({
        message: 'Lessons reordered successfully'
      })
    } catch (error) {
      console.error('Error reordering lessons:', error)
      return APIResponse.error('Failed to reorder lessons', 500)
    }
  },
  {
    requiredRole: 'ADMIN',
    bodySchema: reorderSchema
  }
)
