"use client"

import { useSession } from "next-auth/react"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft, 
  User, 
  LogOut, 
  Settings,
  Target,
  Shield
} from "lucide-react"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { initializeSocket } from "@/lib/socket-client"

interface MinimalLayoutProps {
  children: React.ReactNode
  backUrl?: string
  backLabel?: string
  title?: string
  userRole?: 'STUDENT' | 'ADMIN'
}

export function MinimalLayout({ 
  children, 
  backUrl, 
  backLabel = "Back",
  title,
  userRole 
}: MinimalLayoutProps) {
  const { data: session } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (session?.user) {
      initializeSocket({
        id: session.user.id,
        name: session.user.name || '',
        email: session.user.email || '',
        role: session.user.role || userRole || 'STUDENT',
        token: 'session-token'
      })
    }
  }, [session, userRole])

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }

  const getDefaultBackUrl = () => {
    if (userRole === 'ADMIN') {
      return '/admin'
    }
    return '/student'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      {/* Minimal Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-sm">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          {/* Left side - Back button and title */}
          <div className="flex items-center gap-4">
            {backUrl && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(backUrl)}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {backLabel}
              </Button>
            )}
            
            {!backUrl && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(getDefaultBackUrl())}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {userRole === 'ADMIN' ? 'Admin Dashboard' : 'Student Dashboard'}
              </Button>
            )}

            {title && (
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  {userRole === 'ADMIN' ? (
                    <Shield className="h-5 w-5 text-white" />
                  ) : (
                    <Target className="h-5 w-5 text-white" />
                  )}
                </div>
                <div>
                  <h1 className="font-semibold text-lg">{title}</h1>
                  <p className="text-xs text-muted-foreground">
                    {userRole === 'ADMIN' ? 'Admin Panel' : 'Student Portal'}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Right side - User menu */}
          <div className="flex items-center gap-3">
            {userRole && (
              <Badge variant={userRole === 'ADMIN' ? 'default' : 'secondary'}>
                {userRole}
              </Badge>
            )}
            
            {session?.user && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={session.user.image || ""} />
                      <AvatarFallback>
                        {session.user.name?.split(' ').map(n => n[0]).join('') || 'U'}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-1 leading-none">
                      <p className="font-medium">{session.user.name}</p>
                      <p className="w-[200px] truncate text-sm text-muted-foreground">
                        {session.user.email}
                      </p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => router.push(userRole === 'ADMIN' ? '/admin/profile' : '/student/profile')}>
                    <User className="mr-2 h-4 w-4" />
                    Profile
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => router.push(userRole === 'ADMIN' ? '/admin/settings' : '/student/settings')}>
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 container mx-auto px-4 py-6">
        {children}
      </main>
    </div>
  )
}
