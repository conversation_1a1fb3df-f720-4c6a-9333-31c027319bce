const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function addTestReviews() {
  try {
    // Find the Data Science course
    const course = await prisma.course.findUnique({
      where: { slug: 'data-science' }
    })

    if (!course) {
      console.log('Course not found')
      return
    }

    // Find the current user (instructor)
    const user = await prisma.user.findFirst({
      where: { email: { contains: '@' } }
    })

    if (!user) {
      console.log('User not found')
      return
    }

    // Create some test reviews
    const reviews = [
      {
        courseId: course.id,
        userId: user.id,
        rating: 5,
        title: 'Excellent Course!',
        comment: 'This course exceeded my expectations. The content is well-structured and easy to follow. I learned so much about data science fundamentals.',
        isPublic: true
      },
      {
        courseId: course.id,
        userId: user.id,
        rating: 4,
        title: 'Great for beginners',
        comment: 'Perfect introduction to data science. The instructor explains complex concepts in simple terms.',
        isPublic: true
      },
      {
        courseId: course.id,
        userId: user.id,
        rating: 5,
        title: 'Highly recommended',
        comment: 'Amazing course with practical examples. I can now apply what I learned in real projects.',
        isPublic: true
      }
    ]

    // Note: This will fail due to unique constraint, but let's try anyway
    for (const review of reviews) {
      try {
        await prisma.courseReview.create({
          data: review
        })
        console.log('Created review:', review.title)
      } catch (error) {
        console.log('Review already exists or error:', error.message)
      }
    }

    console.log('Test reviews setup complete')

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addTestReviews()
